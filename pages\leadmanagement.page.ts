import { Page } from '@playwright/test';
import { allure } from "allure-playwright";

export class LeadManagementPage {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    async navigateToLeadManagement() {
        await this.page.locator("//div[@id='LeftSideBar']//ul[@class='main-menu']/li[2]/div").click();
        await this.page.click('ul#CRM_collapse li:nth-child(2) a');
        await this.page.waitForTimeout(3000);
    }

    async resetFilters() {
        await this.page.locator("//a[@id='LeadDataListObj-jdgrid-reset-filter']").click();
        await this.page.waitForTimeout(10000);
    }

    async applyFilters() {
        await this.page.locator("//a[@id='LeadDataListObj-jdgrid-search-dropdown-filter']").click();
        await this.page.locator("//input[@id='EligibleForYear-input']").click();
        for (let i = 1; i <= 11; i += 7) {
            await this.page.locator(`//div[@id='filters-parent']/div[5]/div/label[${i}]`).click();
            await this.page.waitForTimeout(1000);
        }
        await this.page.locator("//a[@id='LeadDataListObj-jdgrid-apply-filter']").click();
        await this.page.waitForTimeout(5000);
        await allure.attachment("LeadManagementFilters.png", await this.page.screenshot(), "image/png");
    }

    async selectRandomLead() {
        const tableBody = await this.page.$('tbody.jdtable-body');
        const tableRows = await tableBody.$$('tr.jdtable-row');
        if (tableRows.length > 0) {
            const randomIndex = Math.floor(Math.random() * tableRows.length);
            const randomRow = tableRows[randomIndex];
            await randomRow.click();
        } else {
            throw new Error("No leads found");
        }
        await this.page.waitForTimeout(6000);
    }

    async navigateToInteractionHistory() {
        await this.page.locator("//div[@id='lead-details']/ul[@role='tablist']/li[2]/a[@role='tab']").click();
        await this.page.waitForTimeout(2000);
        await allure.attachment("InteractionHistory.png", await this.page.screenshot(), "image/png");
    }

    async navigateToLeadTags() {
        try {
            await this.page.locator('a[href="#Lead_Tags"]').click();
        } catch (e) {
            console.log("Lead has no tags");
        }
    }

    async addTag() {
        await this.page.waitForTimeout(1000);
        await this.page.click('button#c145');

        await this.page.locator("//div[@id='selectLeadTagModal']/div[@class='modal-dialog modal-lg']/div[@class='modal-content']//div[@class='col-md-12']/div//div[@class='customselect-dropdown form-control fullWidth']").click();
        const visibleTag = this.page.locator("//div[@id='selectLeadTagModal']/div[@class='modal-dialog modal-lg']/div[@class='modal-content']/div[@class='modal-body']//div[@class='col-md-12']/div//div[@class='customselect-dropdown-list-wrapper']//label[.='HOT']");

        if (await visibleTag.isVisible()) {
            console.log("Tag is visible");
        }
        await this.page.waitForTimeout(1000);
        await allure.attachment("LeadTagModal.png", await this.page.screenshot(), "image/png");

        await this.page.locator("//div[@id='selectLeadTagModal']/div[@class='modal-dialog modal-lg']//div[@class='modal-header']/button[@type='button']/span[.='×']").click();
        await this.page.waitForTimeout(2000);
    }

    async navigateToOtherTab() {
        try {
            const otherTab = this.page.locator("//div[@id='lead-details']/ul[@role='tablist']/li[4]/a[@role='tab']");
            await otherTab.click();
        } catch (e) {
            console.error("Other tab is not clickable");
        }
        await this.page.waitForTimeout(3000);
        await allure.attachment("LeadManagement.png", await this.page.screenshot(), "image/png");
    }
}
