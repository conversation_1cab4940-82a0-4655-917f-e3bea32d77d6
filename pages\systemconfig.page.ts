import { Page, Locator, expect } from "@playwright/test";

export class SystemConfigPage {
    readonly page: Page;
    readonly systemConfigLink: Locator;
    readonly workflow: Locator;
    readonly newEnrolment: Locator;
    readonly enrolmentLink: Locator;
    readonly newEnrolmentStatus: Locator;
    readonly distanceAccredited: Locator;
    readonly distanceFCAdC: Locator;
    readonly addWorkflow: Locator;
    readonly workflowHeading: Locator;
    readonly enrolPaymentPlan: Locator;
    readonly addTemplate: Locator;
    readonly templateHeading: Locator;
    readonly devicePaymentPlan: Locator;
    readonly deviceTemplateHeading: Locator;

    constructor(page: Page) {
        this.page = page;
        this.systemConfigLink = page.getByText('System Configuration', { exact: true });
        this.workflow = page.getByRole('link', { name: 'Workflow', exact: true });
        this.newEnrolment = page.getByRole('cell', {name: 'NewEnrolment25'});
        this.enrolmentLink = page.getByLabel('Workflow Url');
        this.newEnrolmentStatus = page.locator('#c13');
        this.distanceAccredited = page.getByRole('cell', {name: 'Dist Accredited'});
        this.distanceFCAdC = page.getByRole('cell', {name: 'Dist_FC_&_AdC'});
        this.addWorkflow = page.getByRole('button', {name: 'Add Workflow'});
        this.workflowHeading = page.locator('text=Workflow Details');
        this.enrolPaymentPlan = page.getByRole('link', { name: 'Enrolment', exact: true }).nth(1);
        this.addTemplate = page.getByRole('button', {name: 'Add Template'});
        this.templateHeading = page.locator('text=Enrolment Payment Plan Template Details');
        this.devicePaymentPlan = page.getByRole('link', { name: 'Device', exact: true });
        this.deviceTemplateHeading = page.locator('text=Device Payment Plan Template Details');
    }

    async navigateToSystemConfig(): Promise<void> {
        await this.systemConfigLink.click();
    }

    async navigateToWorkflow(): Promise<void> {
        await this.workflow.click();
    }

    async clickNewEnrolment(): Promise<void> {
        await this.newEnrolment.click();
    }

    async clickDistanceAccredited(): Promise<void> {
        await this.distanceAccredited.click();
    }

    async clickDistanceFCAdC(): Promise<void> {
        await this.distanceFCAdC.click();
    }

    async clickAddWorkflow(): Promise<void> {
        await this.addWorkflow.click();
    }

    async navigateToEnrolPaymentPlan(): Promise<void> {
        await this.enrolPaymentPlan.click();
    }

    async clickAddTemplate(): Promise<void> {
        await this.addTemplate.click();
    }

    async navigateToDevicePaymentPlan(): Promise<void> {
        await this.devicePaymentPlan.click();
    }

    async navigateToWorkflowPage(): Promise<void> {
        await this.navigateToSystemConfig();
        await this.navigateToWorkflow();
    }

    async checkEnrolmentWorkflow(): Promise<void> {
        await this.clickNewEnrolment();
        await this.page.waitForTimeout(1000);
    }

    async checkDistanceAccreditedWorkflow(): Promise<void> {
        await this.clickDistanceAccredited();
        await this.page.waitForTimeout(1000);
    }

    async checkDistanceNonAccreditedWorkflow(): Promise<void> {
        await this.clickDistanceFCAdC();
        await this.page.waitForTimeout(1000);
    }

    async addNewWorkflow(): Promise<void> {
        await this.clickAddWorkflow();
        await this.page.waitForTimeout(1000);
    }

    async addEnrolmentPaymentPlanTemplate(): Promise<void> {
        await this.navigateToEnrolPaymentPlan();
        await this.clickAddTemplate();
        await this.page.waitForTimeout(1000);
    }

    async addDevicePaymentPlanTemplate(): Promise<void> {
        await this.navigateToDevicePaymentPlan();
        await this.clickAddTemplate();
        await this.page.waitForTimeout(1000);
    }

    async navigateToCalendar(): Promise<void> {
        await this.page.getByRole('link', { name: 'Calendar', exact: true }).click();
    }

    async filterCalendarYear(year: string): Promise<void> {
        try {
            // Wait for and click the Filters link
            await this.page.waitForSelector('a:has-text("Filters")', { state: 'visible', timeout: 5000 });
            await this.page.getByRole('link', { name: 'Filters' }).click();

            // Wait for and click the dropdown - specifically target the second dropdown
            await this.page.waitForSelector('div[data-rel="#c43"]', { state: 'visible', timeout: 5000 });
            await this.page.click('div[data-rel="#c43"] .customselect-dropdown');
            
            // Wait for dropdown list to be visible - specifically target the second dropdown list
            await this.page.waitForSelector('.customselect-dropdown-list >> nth=1', { state: 'visible', timeout: 5000 });
            
            // Uncheck "All" option
            const allCheckbox = this.page.locator('#c43_all_ctl');
            if (await allCheckbox.isChecked()) {
                await allCheckbox.click();
            }
            
            // Wait a bit for the UI to update
            await this.page.waitForTimeout(1000);
            
            // Find and click the checkbox for the specified year using the exact HTML structure
            const yearCheckbox = this.page.locator(`.d-flex input[name="c43"][id="1_opt_ctl"]`);
            await yearCheckbox.waitFor({ state: 'visible', timeout: 5000 });
            
            // Log the number of elements found for debugging
            const count = await yearCheckbox.count();
            if (count === 0) {
                throw new Error(`No checkbox found for year ${year}`);
            }
            if (count > 1) {
                console.warn(`Found ${count} checkboxes matching the selector. Using the first one.`);
            }
            
            // Click the checkbox
            await yearCheckbox.click();
            
            // Wait for and click Apply button
            await this.page.waitForSelector('button:has-text("Apply")', { state: 'visible', timeout: 5000 });
            await this.page.getByRole('button', { name: 'Apply' }).click();
            
            // Wait for the filter to be applied
            await this.page.waitForTimeout(2000);
        } catch (error) {
            console.error(`Failed to filter calendar for year ${year}:`, error);
            throw error;
        }
    }

    async selectCalendar(year: string): Promise<void> {
        // Wait for the table to be loaded
        // await this.page.waitForSelector('c7_dataGrid', { state: 'visible', timeout: 10000 });
        const rows = await this.page.locator('table tbody tr').count();
        console.log(`Number of rows: ${rows}`);
        expect(rows).toBeGreaterThan(0);
        
        // Try different strategies to find and click the calendar
        try {
            // First attempt: Try exact match
            const calendarCell = this.page.getByRole('cell', { name: `Bachelor of Design (110828) - ${year} - 1` }).first();
            if (await calendarCell.isVisible({ timeout: 5000 })) {
                await calendarCell.click();
                return;
            }

            // Second attempt: Try partial match
            const alternativeCell = this.page.getByRole('cell', { name: new RegExp(`.*${year}.*1`) }).first();
            if (await alternativeCell.isVisible({ timeout: 5000 })) {
                await alternativeCell.click();
                return;
            }

            // Third attempt: Try finding by contains text
            const cellByText = this.page.locator(`td:has-text("${year}")`).first();
            if (await cellByText.isVisible({ timeout: 5000 })) {
                await cellByText.click();
                return;
            }

            throw new Error(`Could not find calendar cell for year ${year}`);
        } catch (error) {
            console.error(`Failed to select calendar for year ${year}:`, error);
            throw error;
        }
    }

    async areTermsPopulated(): Promise<boolean> {
        try {
            // Wait for and click the Calendar Weeks tab
            await this.page.getByText('Calendar Weeks').first().click();
            await this.page.waitForTimeout(1000);

            // Wait for the table to be visible
            await this.page.waitForSelector('table tbody tr', { state: 'visible', timeout: 5000 });

            // Find all term dropdowns in the table
            const termDropdowns = this.page.locator('table tbody tr td select.listbox');
            
            // Get the count of dropdowns
            const count = await termDropdowns.count();
            if (count === 0) {
                console.log('No term dropdowns found');
                return false;
            }

            // Check each dropdown until we find one with a selected value
            for (let i = 0; i < count; i++) {
                const dropdown = termDropdowns.nth(i);
                const selectedValue = await dropdown.evaluate((select: HTMLSelectElement) => select.value);
                
                // If we find any dropdown with a value other than 0, terms are populated
                if (selectedValue !== '0') {
                    return true;
                }
            }

            console.log('No populated terms found in any dropdown');
            return false;
        } catch (error) {
            console.error('Error checking terms population:', error);
            return false;
        }
    }
}
