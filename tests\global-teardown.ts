async function globalTeardown() {
  // Clean up only Playwright browser processes
  const { exec } = require('child_process');
  
  if (process.platform === 'win32') {
    // Look for Playwright-specific Chrome instances
    exec('wmic process where "name=\'chrome.exe\' and commandline like \'%--remote-debugging-port%\'" get processid', (err: any, stdout: string) => {
      if (err) {
        console.log('Error finding Playwright Chrome processes:', err);
        return;
      }
      
      // Extract PIDs and kill only those processes
      const pids = stdout.split('\n')
        .map(line => line.trim())
        .filter(line => line && line !== 'ProcessId')
        .forEach(pid => {
          exec(`taskkill /F /PID ${pid}`, (killErr: any) => {
            if (killErr) {
              console.log(`Error killing process ${pid}:`, killErr);
            }
          });
        });
    });
  } else {
    // For Unix-like systems, use a more precise grep
    exec('ps aux | grep chrome | grep -i "remote-debugging-port" | awk \'{print $2}\'', (err: any, stdout: string) => {
      if (err) {
        console.log('Error finding Playwright Chrome processes:', err);
        return;
      }
      
      stdout.split('\n')
        .filter(pid => pid)
        .forEach(pid => {
          exec(`kill -9 ${pid}`, (killErr: any) => {
            if (killErr) {
              console.log(`Error killing process ${pid}:`, killErr);
            }
          });
        });
    });
  }

  // Clean up test artifacts older than 24 hours
  const fs = require('fs');
  const path = require('path');
  
  const cleanupDirectories = [
    './test-results/screenshots',
    './test-results/videos',
    './test-results/traces'
  ];

  const ONE_DAY = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
  const now = new Date().getTime();

  cleanupDirectories.forEach(dir => {
    if (fs.existsSync(dir)) {
      fs.readdirSync(dir).forEach((file: string) => {
        const filePath = path.join(dir, file);
        const stats = fs.statSync(filePath);
        
        if (now - stats.mtime.getTime() > ONE_DAY) {
          try {
            fs.unlinkSync(filePath);
            console.log(`Cleaned up old test artifact: ${filePath}`);
          } catch (err) {
            console.error(`Error cleaning up ${filePath}:`, err);
          }
        }
      });
    }
  });
}

export default globalTeardown;
