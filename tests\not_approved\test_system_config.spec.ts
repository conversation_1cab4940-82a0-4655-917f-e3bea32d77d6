import { expect } from '@playwright/test';
import * as allure from "allure-js-commons";
import { navigateWithBotAuth, navigateWithHelperBotAuth } from '../../test-objects/login_process';
import { Severity } from "allure-js-commons";
import { SystemConfigPage } from "../../pages/systemconfig.page";
import { test } from '../../test-objects/auth.fixture';

function errorToErrorLike(err: unknown): { message?: string; stack?: string; } {
    if (err instanceof Error) {
        return err;
    } else if (typeof err === 'string') {
        return { message: err };
    } else {
        return { message: String(err) };
    }
}

test.describe('System Configuration Test Suite', () => {
    let systemConfigPage: SystemConfigPage;

    test('Contact Enrolment Workflow', async ({ botAuth, logger }) => {
        systemConfigPage = new SystemConfigPage(botAuth.page)

        await allure.description("This test is to validate that the Contact enrolment workflow is visible and active");
        await allure.severity(Severity.NORMAL);
        logger.info('Starting Contact Enrolment Workflow test');

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await allure.step("Navigate to the Workflow page", async () => {
            try {
                logger.debug('Navigating to System Configuration');
                await systemConfigPage.navigateToSystemConfig();
                logger.debug('Navigating to Workflow');
                await systemConfigPage.navigateToWorkflow();
                logger.info('Successfully navigated to Workflow page');
            } catch (error) {
                logger.error('Failed to navigate to Workflow page', errorToErrorLike(error));
                throw error;
            }
        });

        await allure.step("Checking that the current year link is active and correct", async () => {
            try {
                logger.debug('Clicking New Enrolment');
                await systemConfigPage.clickNewEnrolment();
                
                logger.debug('Verifying enrolment link');
                await expect(systemConfigPage.enrolmentLink).toHaveValue(/https:\/\/.*\/App\/Student\/Workflow\/NewEnrolment25\/2025$/);
                
                logger.debug('Verifying enrolment status');
                await expect(systemConfigPage.newEnrolmentStatus).toHaveValue('0');
                
                logger.info('Successfully verified current year link and status');
            } catch (error) {
                logger.error('Failed to verify enrolment workflow', errorToErrorLike(error));
                throw error;
            }
        });
    });

    test('Distance Accredited Enrolment Workflow', async ({ helperBotAuth, logger }) => {
        systemConfigPage = new SystemConfigPage(helperBotAuth.page)
        await allure.description("This test is to validate that the Distance Accredited enrolment workflow is visible and active");
        await allure.severity(Severity.NORMAL);
        logger.info('Starting Distance Accredited Enrolment Workflow test');

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
        });

        await allure.step("Navigate to the Workflow page", async () => {
            try {
                logger.debug('Navigating to System Configuration');
                await systemConfigPage.navigateToSystemConfig();
                logger.debug('Navigating to Workflow');
                await systemConfigPage.navigateToWorkflow();
                logger.info('Successfully navigated to Workflow page');
            } catch (error) {
                logger.error('Failed to navigate to Workflow page', errorToErrorLike(error));
                throw error;
            }
        });

        await allure.step("Checking that the current year link is active and correct", async () => {
            try {
                logger.debug('Clicking Distance Accredited');
                await systemConfigPage.clickDistanceAccredited();
                
                logger.debug('Verifying enrolment link');
                await expect(systemConfigPage.enrolmentLink).toHaveValue(/https:\/\/.*\/App\/Student\/Workflow\/Dist Accredited\/2024$/);
                
                logger.debug('Verifying enrolment status');
                await expect(systemConfigPage.newEnrolmentStatus).toHaveValue('0');
                
                logger.info('Successfully verified current year link and status');
            } catch (error) {
                logger.error('Failed to verify enrolment workflow', errorToErrorLike(error));
                throw error;
            }
        });
    });

    test('Distance Non-Accredited Enrolment Workflow', async ({ botAuth, logger }) => {
        systemConfigPage = new SystemConfigPage(botAuth.page);
        await allure.description("This test is to validate that the Distance Non-Accredited enrolment workflow is visible and active");
        await allure.severity(Severity.NORMAL);
        logger.info('Starting Distance Non-Accredited Enrolment Workflow test');

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await allure.step("Navigate to the Workflow page", async () => {
            await botAuth.page.waitForTimeout(5000);
            try {
                logger.debug('Navigating to System Configuration');
                await systemConfigPage.navigateToSystemConfig();
                logger.debug('Navigating to Workflow');
                await systemConfigPage.navigateToWorkflow();
                logger.info('Successfully navigated to Workflow page');
            } catch (error) {
                logger.error('Failed to navigate to Workflow page', errorToErrorLike(error));
                throw error;
            }
        });

        await allure.step("Checking that the current year link is active and correct", async () => {
            try {
                logger.debug('Clicking Distance FC AdC');
                await systemConfigPage.clickDistanceFCAdC();
                
                logger.debug('Verifying enrolment link');
                await expect(systemConfigPage.enrolmentLink).toHaveValue(/https:\/\/.*\/App\/Student\/Workflow\/Dist_FC_&_AdC\/2024$/);
                
                logger.debug('Verifying enrolment status');
                await expect(systemConfigPage.newEnrolmentStatus).toHaveValue('0');
                
                logger.info('Successfully verified current year link and status');
            } catch (error) {
                logger.error('Failed to verify enrolment workflow', errorToErrorLike(error));
                throw error;
            }
        });
    });

    test('Add a Workflow', async ({ helperBotAuth, logger }) => {
        systemConfigPage = new SystemConfigPage(helperBotAuth.page)
        await allure.description("This test is to validate that an enrolment workflow can be added");
        await allure.severity(Severity.NORMAL);
        logger.info('Starting Add Workflow test');

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
        });

        await allure.step("Navigate to the Workflow page", async () => {
            try {
                logger.debug('Navigating to System Configuration');
                await systemConfigPage.navigateToSystemConfig();
                logger.debug('Navigating to Workflow');
                await systemConfigPage.navigateToWorkflow();
                logger.info('Successfully navigated to Workflow page');
            } catch (error) {
                logger.error('Failed to navigate to Workflow page', errorToErrorLike(error));
                throw error;
            }
        });

        await allure.step("Add an enrolment workflow and check that the modal is displayed", async () => {
            try {
                logger.debug('Clicking Add Workflow button');
                await systemConfigPage.clickAddWorkflow();
                
                logger.debug('Verifying workflow heading visibility');
                await expect(systemConfigPage.workflowHeading).toBeVisible();
                
                logger.info('Successfully verified workflow modal display');
            } catch (error) {
                logger.error('Failed to add workflow or verify modal', errorToErrorLike(error));
                throw error;
            }
        });
    });

    test('Add an Enrolment Payment Plan Template', async ({ botAuth, logger }) => {
        systemConfigPage = new SystemConfigPage(botAuth.page)
        await allure.description("This test is to validate that an Enrolment payment plan template can be added");
        await allure.severity(Severity.NORMAL);
        logger.info('Starting Add Enrolment Payment Plan Template test');

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await allure.step("Navigate to the Enrolment Payment Plan page", async () => {
            try {
                logger.debug('Navigating to System Configuration');
                await systemConfigPage.navigateToSystemConfig();
                logger.debug('Navigating to Enrolment Payment Plan');
                await systemConfigPage.navigateToEnrolPaymentPlan();
                logger.info('Successfully navigated to Enrolment Payment Plan page');
            } catch (error) {
                logger.error('Failed to navigate to Enrolment Payment Plan page', errorToErrorLike(error));
                throw error;
            }
        });

        await allure.step("Add a payment plan template and check that the modal pops up", async () => {
            try {
                logger.debug('Clicking Add Template button');
                await systemConfigPage.clickAddTemplate();
                
                logger.debug('Verifying template heading visibility');
                await expect(systemConfigPage.templateHeading).toBeVisible();
                
                logger.info('Successfully verified template modal display');
            } catch (error) {
                logger.error('Failed to add template or verify modal', errorToErrorLike(error));
                throw error;
            }
        });
    });

    test('Add a Device Payment Plan Template', async ({ helperBotAuth, logger }) => {
        systemConfigPage = new SystemConfigPage(helperBotAuth.page)
        await allure.description("This test is to validate that a Device payment plan template can be added");
        await allure.severity(Severity.NORMAL);
        logger.info('Starting Add Device Payment Plan Template test');

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
        });

        await allure.step("Navigate to the Device Payment Plan page", async () => {
            try {
                logger.debug('Navigating to System Configuration');
                await systemConfigPage.navigateToSystemConfig();
                logger.debug('Navigating to Device Payment Plan');
                await systemConfigPage.navigateToDevicePaymentPlan();
                logger.info('Successfully navigated to Device Payment Plan page');
            } catch (error) {
                logger.error('Failed to navigate to Device Payment Plan page', errorToErrorLike(error));
                throw error;
            }
        });

        await allure.step("Add a payment plan template and check that the modal pops up", async () => {
            try {
                logger.debug('Clicking Add Template button');
                await systemConfigPage.clickAddTemplate();
                
                logger.debug('Verifying device template heading visibility');
                await expect(systemConfigPage.deviceTemplateHeading).toBeVisible();
                
                logger.info('Successfully verified device template modal display');
            } catch (error) {
                logger.error('Failed to add device template or verify modal', errorToErrorLike(error));
                throw error;
            }
        });
    });

    test('Checking that terms are populated in a 2025 Calendar', async ({ botAuth, logger }) => {
        systemConfigPage = new SystemConfigPage(botAuth.page)
        await allure.description("This test is to validate that terms are populated in a 2025 Calendar");
        await allure.severity(Severity.NORMAL);
        logger.info('Starting Checking terms in 2025 Calendar test');

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await allure.step("Navigate to the Calendar page", async () => {
            try {
                logger.debug('Navigating to System Configuration');
                await systemConfigPage.navigateToSystemConfig();
                logger.debug('Navigating to Calendar');
                await systemConfigPage.navigateToCalendar();
                logger.info('Successfully navigated to Calendar page');
            } catch (error) {
                logger.error('Failed to navigate to Calendar page', errorToErrorLike(error));
                throw error;
            }
        });

        await allure.step("Filter and select 2025 Calendar", async () => {
            try {
                logger.debug('Filtering calendar year');
                await systemConfigPage.filterCalendarYear('2025');
                logger.debug('Selecting 2024 calendar');
                await systemConfigPage.selectCalendar('2025');
                logger.info('Successfully filtered and selected 2025 calendar');
            } catch (error) {
                logger.error('Failed to filter or select 2025 calendar', errorToErrorLike(error));
                throw error;
            }
        });

        await allure.step("Check if terms are populated", async () => {
            try {
                logger.debug('Checking if terms are populated');
                const termsPopulated = await systemConfigPage.areTermsPopulated();
                expect(termsPopulated).toBeTruthy();
                logger.info('Successfully verified terms are populated');
            } catch (error) {
                logger.error('Failed to verify terms are populated', errorToErrorLike(error));
                throw error;
            }
        });
    });

    test.afterEach(async ({ page, logger }) => {
        logger.info('Completing test execution');
        await page.waitForTimeout(1000);
        await allure.attachment("Test Screenshot.png", await page.screenshot(), "image/png");
    });
});
