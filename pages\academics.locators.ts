/**
 * Academics module locators
 * Contains all selectors and locators for the Academics page
 */

export const academicsLocators = {
  // Navigation
  navigation: {
    academicsNavbar: 'div[data-toggle="collapse"][data-target="#Academics_collapse"]',
    coursesLink: 'a:has-text("Courses")'
  },

  // Filters
  filters: {
    qualificationTypeSelect: 'select[name="c36"]',
    academicYearSelect: 'select[name="c33"]',
    rise360AcademicYearSelect: 'select[name="c34"]',
    applyFilterButton: 'div#FiltersCollapse div.panel-body div:nth-child(5) div button[name="c38"]'
  },

  // Course grid and selection
  courses: {
    courseList: 'div#courses div.row div div:nth-child(2) div.table-responsive table tbody tr td:first-child',
    courseGrid: 'div#courses'
  },

  // Subjects
  subjects: {
    subjectsTab: 'tab[name="Subjects"]',
    subjectGrid: '#c96_dataGrid tbody tr',
    addSubjectButton: 'button[name="Add Subject"]'
  },

  // Qualification management
  qualifications: {
    tabList: 'form#Qualification_OverviewForm ul[role="tablist"] li:nth-child(3) a[role="tab"]',
    addButton: 'div#qualifications button[name="c1"]',
    modal: 'h4#QualificationModalLabel'
  },

  // Course management
  courseManagement: {
    addCourseButton: 'button#AddCourseBtn',
    courseModal: 'h4#CourseModalLabel'
  },

  // Document management
  documents: {
    addDocumentButton: 'button[name="Add Document"]',
    documentModal: 'heading[name="Document Details"]'
  },

  // Offsite and Rise360 specific
  offsite: {
    dropdown: '#c362',
    goToOption: 'li:has-text("Go To")',
    viewContentButton: 'button[name=viewContent]',
    overviewText: 'text=Overview'
  },

  rise360: {
    coursewareTab: 'a[href="#courseware"]',
    tableRow: 'div[id$="_dataGrid"]:has(th:has-text("Courseware Name")) >> table >> tbody >> tr',
    conceptInput: '#concept',
    previewButton: 'button:has-text("Preview")',
    startCourseButton: 'link[name="START COURSE"]',
    lessonTitle: '#page-wrap'
  },

  // Form fields
  forms: {
    commencementDate: '#c10',
    endDate: '#c11',
    submitButton: 'button[name=c7]',
    errorMessage: '.error-message-selector'
  }
};

/**
 * Academics page role-based locators for better accessibility
 */
export const academicsRoleLocators = {
  subjectsTab: { role: 'tab', name: 'Subjects' },
  addSubjectButton: { role: 'button', name: 'Add Subject' },
  addDocumentButton: { role: 'button', name: 'Add Document' },
  documentDetailsHeading: { role: 'heading', name: 'Document Details' },
  courseModuleDetailsHeading: { role: 'heading', name: 'Course Module Details' },
  startCourseLink: { role: 'link', name: 'START COURSE' }
};
