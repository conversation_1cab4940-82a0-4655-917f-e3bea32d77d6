# INCONNECT Test Automation Documentation v1.1

## Table of Contents
- [1. Introduction](#1-introduction)
- [2. Test Automation Strategy](#2-test-automation-strategy)
- [3. Framework Overview](#3-framework-overview)
- [4. Test Environment and Setup](#4-test-environment-and-setup)
- [5. Test Modules and Cases](#5-test-modules-and-cases)
- [6. Test Execution](#6-test-execution)
- [7. Maintenance and Best Practices](#7-maintenance-and-best-practices)
- [8. Quality Metrics and Reporting](#8-quality-metrics-and-reporting)
- [9. Troubleshooting Guide](#9-troubleshooting-guide)

## 1. Introduction

### Overview
This document outlines the test automation suite for the INCONNECT platform, covering most modules. The suite is implemented using Playwright, a powerful tool for web testing and automation. Playwright enables robust, cross-browser testing of various functionalities, ensuring reliability and consistency across different scenarios.

### Purpose
The automation suite aims to validate critical user journeys across all INCONNECT modules, including automating the regression testing that the IT Cluster must conduct.

### Test Objectives
The main objectives of this test suite are:

1. **Customer Relationship Management (CRM)**
   - Validate lead management workflows
   - Test campaign creation and management
   - Verify lead import functionality
   - Test session capture capabilities
   - Ensure proper lead tagging and interaction tracking

2. **Academic Management**
   - Validate qualification creation and management
   - Test course creation and configuration
   - Verify subject creation and management
   - Test brief/document management
   - Validate Rise360 integration
   - Ensure proper date validation for courses
   - Test offsite product accessibility

3. **Finance Management**
   - Verify financial overview functionality
   - Test debtors account management
   - Validate bank import processes
   - Test debit order submissions
   - Verify age analysis reporting
   - Ensure proper date range validation
   - Test export functionality

4. **Enrollment Management**
   - Test enrollment filtering capabilities
   - Validate distance and re-enrollment processes
   - Verify data grid functionality
   - Test active enrollment management

5. **System Configuration**
   - Validate workflow management
   - Test enrolment workflow configuration
   - Verify contact enrollment processes
   - Test template management

6. **Communication Management**
   - Test bulk communication setup
   - Validate triggered communications
   - Verify scheduled communications
   - Test survey communication setup
   - Ensure message group functionality

7. **Compliance and Documentation**
   - Test policies and procedures access
   - Verify document viewing capabilities
   - Validate NLRD data submission
   - Test DHET report management

8. **Exchange Management**
   - Verify exchange status management
   - Test exchange opening/closing functionality
   - Validate status transitions

9. **Analytics and Reporting**
   - Test report category creation
   - Validate report generation
   - Verify data analytics viewing
   - Test filtering capabilities

## 2. Test Automation Strategy

### Technology Stack
- **Core Framework**: Playwright v1.43.0
- **Language**: TypeScript
- **Reporting**: Allure Reporter with enhanced environment details
- **Test Data Management**: Faker.js
- **Configuration**: dotenv for environment management
- **Logging**: Custom structured logging system

### Design Patterns
1. **Page Object Model**
   - Separate page classes for each module
   - Encapsulated UI interactions
   - Reusable page methods

2. **Test Object Pattern**
   - Modular test data management
   - Reusable test utilities
   - Shared login processes

### Framework Architecture
```mermaid
graph TD
    A[Test Specs] --> B[Page Objects]
    B --> C[Test Objects]
    C --> D[Core Framework]
    D --> E[Playwright Engine]
    E --> F[Application Under Test]
    G[Configuration] --> D
    H[Test Data] --> C
    I[Allure Reporting] --> A
    J[Logger] --> D
    K[Auth States] --> D
    L[Jenkins Pipeline] --> M[Test Execution]
    M --> N[Allure Reports]
    M --> O[Playwright Reports]
```

## 3. Framework Overview

### Required Components
1. **Core Dependencies**
   - Node.js (latest LTS version)
   - @playwright/test
   - allure-playwright
   - allure-js-commons
   - @faker-js/faker

2. **Custom Modules**
   - login_process.js (authentication)
   - campaign_objects.js (Campaign-specific objects)
   - academics_objects.js (Academics-specific objects)
   - Other module-specific page objects

### Project Structure
```plaintext
INCONNECT-Test-Management/
├── tests/
│   ├── approved_tests/    # Production-ready test specs
│   ├── not_approved/      # Tests under review
│   └── auth.setup.ts      # Authentication setup
├── pages/                 # Page object definitions
├── test-objects/          # Test data and objects
├── lib/                   # Shared utilities
│   ├── logger.ts         # Enhanced logging system
│   └── test-logger.ts    # Test-specific logging
├── allure-results/       # Test execution results
├── allure-report/        # Generated reports
├── coverage/             # Code coverage reports
└── docs/                 # Documentation
```

## 4. Test Environment and Setup

### Environment Configuration
```typescript
// playwright.config.ts
export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  workers: 2,
  timeout: 250000,
  projects: [
    {
      name: 'bot-user-tests',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'helper-bot-tests',
      use: { ...devices['Desktop Chrome'] }
    }
  ]
});
```

### Authentication States
The framework supports multiple authentication states:
- bot-user.json: Primary test automation user
- helper-bot-user.json: Secondary test user for specific scenarios

### Logging System
```typescript
enum LogLevel {
    DEBUG = 'DEBUG',
    INFO = 'INFO',
    WARN = 'WARN',
    ERROR = 'ERROR'
}

// Usage example
const logger = Logger.getInstance();
logger.info('Test started', { testName: 'Example Test' });
logger.error('Test failed', error, { step: 'Login' });
```

### Test Data Management
The tests rely on:
- Valid bot login credentials
- Existing campaigns for random selection
- Existing courses and qualifications
- Access to Rise360 content
- Generated test data using Faker.js

## 5. Test Modules and Cases

### CRM Module

1. **Lead Management**
   - Reset and apply filters
   - Select random leads
   - Navigate interaction history
   - Manage lead tags
   - Verify lead details

2. **Campaign Management**
   - Create new campaigns
   - Add leads to campaigns
   - Manage campaign sessions
   - Import lead data
   - Verify campaign modals

### Academic Module

1. **Qualification Management**
   - Create new qualifications
   - Verify qualification details
   - Manage qualification settings
   - Test validation rules

2. **Course Management**
   - Create new courses
   - Set course dates
   - Validate date constraints
   - Manage course details

3. **Subject Management**
   - Create new subjects
   - Link to courses
   - Manage subject content
   - Test subject validation

4. **Content Integration**
   - Access Rise360 content
   - Verify content upload
   - Preview courseware
   - Test offsite products

### Finance Module

1. **Financial Overview**
   - View financial data
   - Test date range filters
   - Export financial data
   - Validate data grid

2. **Debtors Management**
   - Add debtors accounts
   - Apply debtor filters
   - Verify account details
   - Test modal functionality

3. **Banking Operations**
   - Process bank imports
   - Verify CSV imports
   - Test validation rules
   - Manage transactions

4. **Debit Orders**
   - Submit debit orders
   - Select currencies
   - Verify submission process
   - Test validation rules

5. **Age Analysis**
   - Generate reports
   - Select campuses
   - Set date ranges
   - Validate report data

### System Configuration Module

1. **Workflow Management**
   - Add new workflows
   - Configure enrolment workflows
   - Test workflow activation
   - Verify workflow links

2. **Template Management**
   - Create templates
   - Configure settings
   - Test template validation
   - Verify template usage

### Communication Module

1. **Communication Setup**
   - Configure bulk communications
   - Set up triggered communications
   - Manage scheduled communications
   - Configure survey communications

2. **Message Management**
   - Create message groups
   - Test message delivery
   - Verify communication flows
   - Validate message templates

### Compliance Module

1. **Document Management**
   - Access policies
   - View procedures
   - Test PDF viewer
   - Verify document access

2. **Regulatory Compliance**
   - Submit NLRD data
   - Generate DHET reports
   - Verify submission status
   - Test report generation

### Exchange Module

1. **Status Management**
   - Toggle exchange status
   - Verify status changes
   - Test status visibility
   - Validate state transitions

### Analytics Module

1. **Report Management**
   - Create report categories
   - Generate new reports
   - View analytics data
   - Test report filters

2. **Data Analysis**
   - View data grids
   - Apply filters
   - Export reports
   - Validate data accuracy

## 6. Test Execution

### Running Tests
```bash
# Run approved tests only
npx playwright test tests/approved_tests/

# Run with specific auth state
npx playwright test --project=bot-user-tests
npx playwright test --project=helper-bot-tests

# Run not approved tests
npx playwright test tests/not_approved/

# Run all tests
npm test

# Run with UI
npm run test:ui

# Run in headed mode
npm run test:headed
```

### Test Approval Workflow
1. New tests are created in not_approved/ directory
2. Tests undergo review and validation
3. Approved tests are moved to approved_tests/ directory
4. Only approved tests run in production environment

### CI/CD Pipeline
The test suite is integrated with Jenkins for continuous integration and automated test execution:

```groovy
// Jenkinsfile configuration
pipeline {
    parameters {
        choice(
            name: 'ENV',
            choices: ['prod', 'staging', 'accp'],
            description: 'Select the environment to run tests against'
        )
        choice(
            name: 'TESTS',
            choices: ['approved-tests', 'not-approved-tests'],
            description: 'Select which test suite to run'
        )
    }
    
    stages {
        stage('Setup') {
            steps {
                bat 'npm install'
                bat 'npx playwright install'
            }
        }
        
        stage('Run Tests') {
            steps {
                bat "npx playwright test --project=${TESTS}"
            }
        }
    }
    
    post {
        always {
            allure([
                results: [[path: 'allure-results']],
                reportBuildPolicy: 'ALWAYS'
            ])
            archiveArtifacts artifacts: 'allure-results/**/*'
            archiveArtifacts artifacts: 'playwright-report/**/*'
        }
    }
}
```

Key CI/CD Features:
- Environment selection (prod/staging/accp)
- Test suite selection (approved/not-approved)
- Automated setup and test execution
- Allure report generation and archival
- Playwright report archival
- Parallel test execution in CI environment

### Generating Reports
```bash
npm run report
```

## 7. Maintenance and Best Practices

### Regular Tasks
1. **Daily**
   - Monitor Jenkins pipeline executions
   - Review Allure reports
   - Check test logs
   - Monitor auth states
   - Investigate flaky tests
   - Update for UI changes

2. **Weekly**
   - Code review and cleanup
   - Test data maintenance
   - Documentation updates
   - Review not approved tests
   - Analyze CI/CD metrics
   - Review pipeline configurations

3. **Monthly**
   - Framework updates
   - Performance review
   - Dependency updates
   - Log rotation and cleanup
   - Pipeline optimization
   - Environment configuration review

### Best Practices
1. **Test Independence**
   - Self-contained tests
   - Clean test data
   - No dependencies between tests

2. **Error Handling**
```typescript
try {
    await allure.step("Action step", async () => {
        // Test action
    });
} catch (error) {
    await allure.attachment(
        "Error Screenshot", 
        await page.screenshot(), 
        "image/png"
    );
    throw error;
}
```

## 8. Quality Metrics and Reporting

### Allure Reports
- Test execution summary
- Step-by-step test flows
- Screenshot attachments
- Environment details
- Execution timeline

### Key Metrics
1. **Test Health**
   - Pass/Fail ratio
   - Flaky test identification
   - Execution time trends

2. **Coverage Metrics**
   - Module coverage
   - Critical path coverage
   - Business scenario coverage

## 9. Troubleshooting Guide

### Common Issues

1. **Flaky Tests**
```typescript
// Add retry logic and logging
test.describe('Flaky Tests', () => {
    test.beforeEach(async ({ page }, testInfo) => {
        logger.info('Starting test', { name: testInfo.title });
        await page.waitForLoadState('networkidle');
    });
});
```

2. **Authentication Issues**
```typescript
// Verify auth state
test.beforeEach(async ({ context }) => {
    const storage = await context.storageState();
    if (!storage.cookies.length) {
        logger.error('Invalid auth state');
        throw new Error('Authentication failed');
    }
});
```

3. **Timeout Issues**
```typescript
// Increase timeout with logging
test.setTimeout(120000);
logger.debug('Increased timeout for slow operation');
```

### Debug Procedures
1. Enable Trace Viewer
2. Review detailed logs
3. Check authentication state
4. Verify test environment
5. Analyze network requests
6. Review screenshot timeline

## Version History

| Version | Date | Description | Author |
|---------|------|-------------|---------|
| 1.0.0 | Aug 2024 | Initial documentation | IT Cluster |
| 1.1.0 | Nov 2024 | Integrated comprehensive framework documentation | IT Cluster |
| 1.2.0 | Feb 2025 | Added test approval workflow, enhanced logging, multiple auth states | IT Cluster |
| 1.2.1 | Feb 2025 | Added Jenkins CI/CD pipeline integration | IT Cluster |