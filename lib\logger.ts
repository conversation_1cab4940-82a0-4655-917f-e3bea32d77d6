import * as fs from 'fs';
import * as path from 'path';

export enum LogLevel {
    DEBUG = 'DEBUG',
    INFO = 'INFO',
    WARN = 'WARN',
    ERROR = 'ERROR'
}

interface ErrorLike {
    message?: string;
    stack?: string;
    name?: string;
    value?: string;
}

export class Logger {
    private static instance: Logger;
    private logDir: string;
    private logFile: string;

    private constructor() {
        this.logDir = path.join(process.cwd(), 'logs');
        this.ensureLogDirectory();
        this.logFile = path.join(this.logDir, `test-execution-${new Date().toISOString().replace(/:/g, '-')}.log`);
    }

    public static getInstance(): Logger {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }

    private ensureLogDirectory(): void {
        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
        }
    }

    private formatMessage(level: LogLevel, message: string, error?: ErrorLike, context?: object): string {
        const timestamp = new Date().toISOString();
        let errorStr = '';
        if (error) {
            errorStr = ` | Error: ${error.message || error.value || 'Unknown error'}`;
            if (error.stack) {
                errorStr += `\nStack: ${error.stack}`;
            }
        }
        const contextStr = context ? ` | Context: ${JSON.stringify(context)}` : '';
        return `[${timestamp}] [${level}] ${message}${errorStr}${contextStr}\n`;
    }

    private writeLog(message: string): void {
        try {
            fs.appendFileSync(this.logFile, message);
            console.log(message.trim());
        } catch (error) {
            console.error('Failed to write to log file:', error);
        }
    }

    public debug(message: string, context?: object): void {
        this.writeLog(this.formatMessage(LogLevel.DEBUG, message, undefined, context));
    }

    public info(message: string, context?: object): void {
        this.writeLog(this.formatMessage(LogLevel.INFO, message, undefined, context));
    }

    public warn(message: string, context?: object): void {
        this.writeLog(this.formatMessage(LogLevel.WARN, message, undefined, context));
    }

    public error(message: string, error?: ErrorLike, context?: object): void {
        this.writeLog(this.formatMessage(LogLevel.ERROR, message, error, context));
    }

    public getLogFilePath(): string {
        return this.logFile;
    }
}
