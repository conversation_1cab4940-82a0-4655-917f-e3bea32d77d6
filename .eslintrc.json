{"root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "parserOptions": {"project": "./tsconfig.json", "ecmaVersion": 2022, "sourceType": "module"}, "rules": {"@typescript-eslint/no-unsafe-argument": "warn", "@typescript-eslint/no-unsafe-assignment": "warn", "@typescript-eslint/no-unsafe-call": "warn", "@typescript-eslint/no-unsafe-member-access": "warn", "@typescript-eslint/no-unsafe-return": "warn", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-explicit-any": "warn"}, "overrides": [{"files": ["tests/**/*.ts", "test-objects/**/*.ts"], "rules": {"@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unsafe-member-access": "off", "@typescript-eslint/no-unsafe-call": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-unsafe-argument": "off"}}], "ignorePatterns": ["node_modules/**", "dist/**", "build/**", "allure-results/**"]}