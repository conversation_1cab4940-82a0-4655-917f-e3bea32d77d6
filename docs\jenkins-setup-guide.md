# Jenkins Setup Guide

## Pipeline Setup (Recommended)

1. Create a new Pipeline job in Jenkins
2. Configure Source Code Management:
   - Select Git
   - Enter repository URL
   - Add GitHub credentials (use token authentication)
   - Select your branch (e.g. main)

3. Pipeline Configuration:
   - Definition: Pipeline script from SCM
   - SCM: Git
   - Repository URL: (same as above)
   - Branch Specifier: (same as above)
   - Script Path: Jenkinsfile

The provided Jenkins<PERSON>le will:
- Install npm dependencies
- Install Playwright browsers
- Run approved tests
- Generate Allure reports
- Archive test results and reports

## Environment Setup

### Manual Setup (Simple)
1. Navigate to your Jenkins workspace:
   ```
   C:\ProgramData\Jenkins\.jenkins\workspace\YOUR_JOB_NAME
   ```
2. Create an `env` folder if it doesn't exist
3. Copy your environment files into this folder:
   - For production: `env/production.env`
   - For staging: `env/staging.env`

The pipeline will automatically use these environment files during test execution.

## Alternative: Freestyle Setup

If you prefer a freestyle project:

1. Create new Freestyle job
2. Configure Git repository and credentials
3. Add build steps:
   ```bash
   npm install
   npx playwright install
   npx playwright test --project=approved-tests
   ```
4. Add post-build actions:
   - Archive artifacts: playwright-report/**/*
   - Generate Allure report from allure-results

## Troubleshooting

- If npm install fails, check Node.js is installed on Jenkins
- If browser installation fails, ensure enough disk space
- For test failures, check playwright-report for details
- For report issues, verify allure-results directory exists
- If tests fail due to missing env files, verify they exist in the workspace
