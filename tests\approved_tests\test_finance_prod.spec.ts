import { test } from '../../test-objects/auth.fixture';
import * as allure from "allure-js-commons";
import { Severity } from "allure-js-commons";
import { FinancePage } from '../../pages/finance.page';
import { navigateWithBotAuth, navigateWithHelperBotAuth } from '../../test-objects/login_process';

test.describe('Test Finance Prod', () => {
    let financePage: FinancePage;
    test.setTimeout(150000);

    test('Financial Overview Prod', async ({ botAuth }) => {
        financePage = new FinancePage(botAuth.page);
        await allure.description("Financial Overview Prod");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform & navigate to Financial Overview", async () => {
            await navigateWithBotAuth(botAuth.page);
            await financePage.navigateToFinancialOverview();
        });

        await allure.step("Select a date: Current date & minus 14 days", async () => {
            const currentDate = new Date();
            const fourteenDaysAgo = new Date(currentDate.getTime() - (30 * 24 * 60 * 60 * 1000));
            const currentDateStr = currentDate.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'short',
                year: 'numeric'
            });
            const fourteenDaysAgoStr = fourteenDaysAgo.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'short',
                year: 'numeric'
            });
            await financePage.selectDateRange(fourteenDaysAgoStr, currentDateStr);
        });

        await allure.step("Wait for the data grid to be visible & Take a screenshot", async () => {
            const isDataGridVisible = await financePage.isDataGridVisible();
            if (isDataGridVisible) {
                console.info("Financial Overview test has passed");
            } else {
                console.info("Financial Overview test has failed");
            }
            await financePage.clickExportButton();
            await financePage.confirmExport();
            await botAuth.page.waitForTimeout(1000);
        });
    });

    test('Debtors Account Prod', async ({ helperBotAuth }) => {
        financePage = new FinancePage(helperBotAuth.page);
        await allure.description("Debtors Account Prod");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform & navigate to Debtors Account", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
            await helperBotAuth.page.waitForTimeout(2000);
            await financePage.navigateToDebtorsAccount();
        });

        await allure.step("Select a debtor and apply the filter", async () => {
            await financePage.selectFutureDebtor();
            await financePage.applyDebtorFilter();
        });

        await allure.step("Add a Debtors Account", async () => {
            await financePage.addDebtorsAccount();
            const isModalVisible = await financePage.isDebtorsAccountModalVisible();
            if (isModalVisible) {
                console.info("Adding a Debtors Account test has passed");
            } else {
                console.info("Adding a Debtors Account test has failed");
            }
        });
    });

    test('Bank Import', async ({ botAuth }) => {
        financePage = new FinancePage(botAuth.page);
        await allure.description("Bank Import Prod");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform & navigate to Bank Import", async () => {
            await navigateWithBotAuth(botAuth.page);
            await financePage.navigateToBankImport();
        });

        await allure.step("Press the Import button and confirm", async () => {
            await financePage.clickImportButton();
            await botAuth.page.waitForTimeout(2000);
            await financePage.confirmImport();
            await botAuth.page.waitForTimeout(5000);
        });

        await allure.step("Check if the CSV import modal is visible", async () => {
            const isCsvImportModalVisible = await financePage.isCsvImportModalVisible();
            if (isCsvImportModalVisible) {
                console.log("CSV import test has passed");
            } else {
                console.error("CSV import test has failed");
            }
        });
    });

    test('Debit Orders', async ({ helperBotAuth }) => {
        financePage = new FinancePage(helperBotAuth.page);
        await allure.description("Debit Orders Prod");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform & navigate to Debit Orders", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
            await financePage.navigateToDebitOrders();
        });

        await allure.step("Add a debit order submission", async () => {
            await financePage.addDebitOrderSubmission();
            await helperBotAuth.page.waitForTimeout(3000);
        });

        await allure.step("Check if the Please Select Currency modal is visible", async () => {
            const isSelectCurrencyModalVisible = await financePage.isSelectCurrencyModalVisible();
            if (isSelectCurrencyModalVisible) {
                console.log("Add a debit order submission test has passed");
            } else {
                console.error("Add a debit order submission test has failed");
            }
        });
    });

    test.skip('Generate an Age Analysis Report', async ({ botAuth }) => {
        financePage = new FinancePage(botAuth.page);
        await allure.description("Age Analysis Prod");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform & navigate to Age Analysis", async () => {
            await navigateWithBotAuth(botAuth.page);
            await financePage.navigateToAgeAnalysis();
        });

        await allure.step("Select a random campus", async () => {
            await financePage.selectRandomCampus();
        });

        await allure.step("Generate the Age Analysis report", async () => {
            const currentDate = new Date();
            const fourteenDaysAgo = new Date(currentDate.getTime() - (14 * 24 * 60 * 60 * 1000));
            const currentDateStr = currentDate.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'short',
                year: 'numeric'
            });
            const fourteenDaysAgoStr = fourteenDaysAgo.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'short',
                year: 'numeric'
            });

            await financePage.generateAgeAnalysisReport(fourteenDaysAgoStr, currentDateStr);
        });

        await allure.step("Wait for the report to be generated", async () => {
            const isReportGenerated = await financePage.waitForReportGeneration();
            if (isReportGenerated) {
                console.log("Age Analysis test has passed");
            } else {
                console.error("Age Analysis test has failed");
            }
        });
    });

    test.skip('Generate an Age Analysis Report without selecting a campus', async ({ botAuth }) => {
        financePage = new FinancePage(botAuth.page);
        await allure.description("Age Analysis Prod");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform & navigate to Age Analysis", async () => {
            await navigateWithBotAuth(botAuth.page);
            await financePage.navigateToAgeAnalysis();
        });
        await allure.step("Generate the Age Analysis report without selecting a campus", async () => {
            await financePage.generateAgeAnalysisReportWithoutSelectingCampus();
        });
    });

    test.skip('Invalid Date Range Selection', async ({ botAuth }) => {
        financePage = new FinancePage(botAuth.page);
        await allure.description("Testing selection of an invalid date range");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(botAuth.page);
            await financePage.navigateToFinancialOverview();
        });

        await allure.step("Select an invalid date range", async () => {
            const invalidFromDate = "31/02/2023"; // Invalid date
            const invalidToDate = "01/01/2023"; // Earlier than from date
            await financePage.selectDateRange(invalidFromDate, invalidToDate);
            await botAuth.page.waitForTimeout(30000);
        });
        const dataGrid = await financePage.isDataGridVisible();
        if (dataGrid) {
            console.log("Data grid is visible. Invalid date range test has failed");
        } else {
            console.log("Data grid is not visible. Invalid date range test has passed");
        }
    });

    test('Export Financial Overview Data Without Date Range', async ({ botAuth }) => {
        financePage = new FinancePage(botAuth.page);
        await allure.description("Testing export functionality without selecting a date range");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(botAuth.page);
            await financePage.navigateToFinancialOverview();
        });

        await allure.step("Attempt to export data without selecting a date range", async () => {
            await financePage.invalidExport();
        });
    });

    test('Add Debtors Account Without Required Fields', async ({ helperBotAuth }) => {
        financePage = new FinancePage(helperBotAuth.page);
        await allure.description("Testing adding a Debtors Account without filling required fields");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
            await financePage.navigateToDebtorsAccount();
        });

        await allure.step("Attempt to add a Debtors Account without filling required fields", async () => {
            await financePage.addDebtorsAccount();
            await financePage.saveDebtorsAccountModalWithoutFind();
        });
    });

    test.afterEach(async ({ page }, testInfo) => {
        if (testInfo.status !== testInfo.expectedStatus) {
            // Take screenshot only on failure
            const screenshot = await page.screenshot();
            await testInfo.attach('screenshot', { body: screenshot, contentType: 'image/png' });
        }
    });
});