# INCONNECT Test Automation Development Guide

## Table of Contents
1. [Project Structure](#project-structure)
2. [Testing Framework Components](#testing-framework-components)
3. [Key Patterns and Best Practices](#key-patterns-and-best-practices)
4. [Adding New Tests](#adding-new-tests)
5. [Environment and Configuration](#environment-and-configuration)
6. [Error Handling and Reporting](#error-handling-and-reporting)
7. [Test Data Management](#test-data-management)

## Project Structure

The project follows a well-organized structure:

```
INCONNECT-Test-Management/
├── pages/              # Page Object Models (POM)
├── test-objects/       # Test data and objects
├── tests/             # Test specifications
├── lib/               # Utility functions
├── docs/              # Project documentation
└── .github/workflows/ # CI/CD configuration
```

### Key Directories and Their Purpose

- **pages/**: Contains Page Object Models for each section of the application
  - Each page class encapsulates the functionality and elements of a specific page
  - Follows the Page Object Pattern for better maintainability

- **test-objects/**: Contains test data and locators
  - Separate files for different modules (e.g., `analytics_objects.ts`, `finance_objects.ts`)
  - Centralized location for all test data and selectors

- **tests/**: Contains test specifications
  - Organized by module/feature
  - Each test file follows consistent patterns for setup, execution, and teardown

- **lib/**: Contains utility functions
  - Custom logger implementation
  - Storage utilities
  - Test helper functions

## Testing Framework Components

### Core Technologies
- **Playwright**: Main testing framework
- **TypeScript**: Programming language
- **Allure**: Test reporting
- **TestRail**: Test case management
- **Custom Logger**: For detailed test execution logging

### Key Dependencies
```json
{
  "@playwright/test": "^1.49.0",
  "allure-playwright": "^3.0.6",
  "@faker-js/faker": "^8.4.1",
  "testrail-integration": "^0.2.5"
}
```

## Key Patterns and Best Practices

### 1. Test Structure Template
```typescript
test.describe('Module Name Test Suite', () => {
    let modulePage: ModulePage;

    test.beforeEach(async ({ page, logger }) => {
        logger.info('Setting up test environment');
        modulePage = new ModulePage(page);
        logger.debug('Page instance created');
    });

    test.afterEach(async ({ page }, testInfo) => {
        // Screenshot capture
        const screenshotPath = `${testInfo.outputDir}/screenshot.png`;
        await page.screenshot({ 
            path: screenshotPath,
            fullPage: true 
        });

        // TestRail metadata
        testInfo.annotations.push({ 
            type: 'testrail_attachment', 
            description: screenshotPath 
        });
    });

    test('Test Case Name', async ({ page, logger }) => {
        await allure.description("Test Description");
        await allure.severity(Severity.NORMAL);
        
        try {
            await allure.step("Step Description", async () => {
                // Test implementation
            });
        } catch (error) {
            logger.error('Error in test', error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    });
});
```

### 2. Page Object Pattern
```typescript
import { Page } from '@playwright/test';
import { ModuleObjects } from "../test-objects/module_objects";

export class ModulePage {
    private page: Page;
    private moduleObjects: ModuleObjects;

    constructor(page: Page) {
        this.page = page;
        this.moduleObjects = new ModuleObjects(page);
    }

    async someAction() {
        try {
            await this.page.click(this.moduleObjects.someSelector);
        } catch (error) {
            console.error("Error performing action:", error);
            throw error;
        }
    }
}
```

### 3. Logging Best Practices
```typescript
// Start of test
logger.info('Starting test execution');

// Debug information
logger.debug('Detailed state or variable information');

// Test step completion
logger.info('Successfully completed step');

// Error handling
logger.error('Error occurred', error, {
    context: 'additional information'
});
```

## Adding New Tests

### 1. Create/Update Page Objects
1. Create a new file in `pages/` directory
2. Follow the Page Object Pattern
3. Include all page-specific actions and elements

### 2. Add Test Objects
1. Create/update file in `test-objects/`
2. Define selectors and test data
3. Export objects for use in page classes

### 3. Write Tests
1. Create test file in `tests/` directory
2. Follow the test structure template
3. Include:
   - Proper logging
   - Allure steps and annotations
   - Error handling
   - TestRail metadata
   - Screenshots for failures

## Environment and Configuration

### 1. Environment Variables
- Create appropriate `.env` files in `env/` directory
- Follow the naming convention: `.env.{environment}`
- Include all necessary configuration variables

### 2. Playwright Configuration
Key settings in `playwright.config.ts`:
```typescript
{
    testDir: './tests',
    timeout: 240000,
    expect: {
        timeout: 30000,
    },
    retries: process.env.CI ? 2 : 1,
    reporter: [
        ["line"],
        ["allure-playwright"],
        ['html'],
        ['junit']
    ]
}
```

## Error Handling and Reporting

### 1. Error Handling Pattern
```typescript
try {
    await someAction();
} catch (error) {
    logger.error('Error description', error instanceof Error ? error : new Error(String(error)));
    await page.screenshot({ path: 'error-screenshot.png' });
    throw error;
}
```

### 2. Reporting
- Allure reports generated after test execution
- TestRail integration for test case management
- Screenshots captured for failed tests
- Detailed logs available in `logs/` directory

## Test Data Management

### 1. Using Storage Utils
```typescript
import { storageUtils } from '../lib/storage-utils';

// Save test data
storageUtils.saveData(testData);

// Retrieve test data
const data = storageUtils.getData();
```

### 2. Generating Test Data
```typescript
import { faker } from '@faker-js/faker';

const testData = {
    name: faker.person.fullName(),
    email: faker.internet.email(),
    phone: faker.phone.number()
};
```

## Best Practices Checklist

When adding new tests or making changes:

- [ ] Follow the Page Object Pattern
- [ ] Include proper logging at all steps
- [ ] Add appropriate error handling
- [ ] Include Allure steps and annotations
- [ ] Add TestRail metadata
- [ ] Take screenshots for failures
- [ ] Update test objects if needed
- [ ] Follow existing naming conventions
- [ ] Add appropriate documentation
- [ ] Include proper test data management
- [ ] Follow the established project structure

## Maintenance and Updates

1. Regularly update dependencies
2. Review and clean up test artifacts
3. Maintain documentation
4. Review and update test data
5. Monitor test execution times
6. Review and update test configurations

This guide serves as a reference for maintaining consistency and quality in the test automation project. Follow these patterns and practices when making changes or additions to the codebase.
