/**
 * Individual module locators
 * Contains all selectors and locators for the Individual page
 */

export const individualLocators = {
  // Navigation and main buttons
  newIndividualButton: 'button[name="New Individual"]',
  
  // Student grid
  studentGrid: {
    table: '#c54_dataGrid table tbody tr',
    visibleRows: '#c54_dataGrid table tbody tr'
  },
  
  // Document management
  documents: {
    tab: 'a[href="#individual_docs"]',
    addButton: 'button[name="Add Document"]',
    modal: {
      header: '.modal-header h4#PersonalDocumentModalLabel',
      documentTypeLabel: 'text=Document Type',
      documentTypeDropdown: 'select#c62',
      documentTypeCombobox: 'combobox'
    }
  }
};

/**
 * Individual page role-based locators for better accessibility
 */
export const individualRoleLocators = {
  newIndividualButton: { role: 'button', name: 'New Individual' },
  addDocumentButton: { role: 'button', name: 'Add Document' },
  documentTypeLabel: { text: 'Document Type' },
  documentTypeCombobox: { role: 'combobox' }
};
