import { Page, BrowserContext } from '@playwright/test';
import { test as loggerTest } from '../lib/test-logger';

type AuthFixtures = {
    botAuth: { context: BrowserContext; page: Page };
    helperBotAuth: { context: BrowserContext; page: Page};
    exchangeBotAuth: { context: BrowserContext; page: Page };
};

// Extend the logger test with auth configurations (combines both fixtures)
export const test = loggerTest.extend<AuthFixtures>({
    // Bot user configuration
    botAuth: async ({ browser }, use) => {
        const context = await browser.newContext({
            storageState: 'playwright/.auth/bot-user.json'
        });
        const page = await context.newPage();
        await use({ context, page });
        await context.close();
    },

    // Helper bot user configuration
    helperBotAuth: async ({ browser }, use) => {
        const context = await browser.newContext({
            storageState: 'playwright/.auth/helper-bot-user.json'
        });
        const page = await context.newPage();
        await use({ context, page });
        await context.close();
    },

    // Exchange bot user configuration
    exchangeBotAuth: async ({ browser }, use) => {
        const context = await browser.newContext({
            storageState: 'playwright/.auth/exchange-bot-user.json'
        });
        const page = await context.newPage();
        await use({ context, page });
        await context.close();
    }
});
