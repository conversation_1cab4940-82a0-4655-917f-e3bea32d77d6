import { test } from '../../test-objects/auth.fixture';
import { GraduationPage } from '../../pages/graduation.page';
import * as allure from "allure-js-commons";
import { Severity } from "allure-js-commons";
import { expect } from '@playwright/test';
import { navigateWithBotAuth } from '../../test-objects/login_process';
import { GraduationStatus } from '../../test-objects/graduation_objects';

test.describe('Graduation Module Tests', () => {
    let graduationPage: GraduationPage;

    test('should be able to navigate to graduation page and view student details', async ({ botAuth }) => {
        graduationPage = new GraduationPage(botAuth.page);

        await allure.description("Testing the navigation to graduation page and viewing student details");
        await allure.severity(Severity.BLOCKER);
        await allure.feature("Graduation");

        await allure.step('Navigate to login page', async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await allure.step('Navigate to Graduation page', async () => {
            await graduationPage.navigateToGraduation();
            await expect(await graduationPage.isGraduationOverviewPage()).toBeTruthy();
        });

        await allure.step('Select random student and verify modal', async () => {
            // Select random student and verify modal appears
            await graduationPage.selectRandomStudent();
            await expect(await graduationPage.isGraduationDetailsModalVisible()).toBeTruthy();
        });
    });

    test('should be able to navigate between graduation status tabs', async ({ helperBotAuth }) => {
        graduationPage = new GraduationPage(helperBotAuth.page);

        await allure.description("Testing the navigation between different graduation status tabs");
        await allure.severity(Severity.NORMAL);
        await allure.feature("Graduation");

        await allure.step('Navigate to login page', async () => {
            await navigateWithBotAuth(helperBotAuth.page);
        });

        await test.step('Navigate to Graduation page', async () => {
            await graduationPage.navigateToGraduation();
            await expect(await graduationPage.isGraduationOverviewPage()).toBeTruthy();
        });

        await test.step('Navigate through all tabs', async () => {
            await graduationPage.navigateToTab(GraduationStatus.VERIFIED);
            await graduationPage.navigateToTab(GraduationStatus.READY_FOR_GRADUATION);
            await graduationPage.navigateToTab(GraduationStatus.ARCHIVED);
            await graduationPage.navigateToTab(GraduationStatus.FAILED);
            await graduationPage.navigateToTab(GraduationStatus.IN_PROGRESS);
        });
    });
});
