import { Page, Locator } from '@playwright/test';
import { graduationLocators, graduationRoleLocators, GraduationStatus, GraduationDetails } from './graduation.locators';

export class GraduationPage {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    // Locator getters
    get graduationLink(): Locator {
        return this.page.getByText(graduationRoleLocators.graduationLink.text);
    }

    get graduationHeader(): Locator {
        return this.page.locator('h3:has-text("Graduation Overview")');
    }

    get activeTabTable(): Locator {
        return this.page.locator(graduationLocators.dataGrid.activeTabTable);
    }

    get studentRows(): Locator {
        return this.activeTabTable.locator(graduationLocators.dataGrid.studentRow);
    }

    get inProgressTab(): Locator {
        return this.page.locator(graduationLocators.tabs.inProgress);
    }

    get verifiedTab(): Locator {
        return this.page.locator(graduationLocators.tabs.verified);
    }

    get readyForGraduationTab(): Locator {
        return this.page.locator(graduationLocators.tabs.readyForGraduation);
    }

    get archivedTab(): Locator {
        return this.page.locator(graduationLocators.tabs.archived);
    }

    get failedTab(): Locator {
        return this.page.locator(graduationLocators.tabs.failed);
    }

    get graduationDetailsModal(): Locator {
        return this.page.locator(graduationLocators.modal.header);
    }

    get modalStudentName(): Locator {
        return this.page.locator(graduationLocators.modal.studentName);
    }

    get modalStudentNumber(): Locator {
        return this.page.locator(graduationLocators.modal.studentNumber);
    }

    get modalIdNumber(): Locator {
        return this.page.locator(graduationLocators.modal.idNumber);
    }

    get modalCampus(): Locator {
        return this.page.locator(graduationLocators.modal.campus);
    }

    get modalGraduationDate(): Locator {
        return this.page.locator(graduationLocators.modal.graduationDate);
    }

    get saveButton(): Locator {
        return this.page.locator(graduationLocators.modal.saveButton);
    }

    get archiveButton(): Locator {
        return this.page.locator(graduationLocators.modal.archiveButton);
    }

    get deleteButton(): Locator {
        return this.page.locator(graduationLocators.modal.deleteButton);
    }

    get cancelButton(): Locator {
        return this.page.locator(graduationLocators.modal.cancelButton);
    }

    // Helper method to get tab by status
    getTabByStatus(status: GraduationStatus): Locator {
        const tabMapping = {
            [GraduationStatus.IN_PROGRESS]: this.inProgressTab,
            [GraduationStatus.VERIFIED]: this.verifiedTab,
            [GraduationStatus.READY_FOR_GRADUATION]: this.readyForGraduationTab,
            [GraduationStatus.ARCHIVED]: this.archivedTab,
            [GraduationStatus.FAILED]: this.failedTab
        };
        return tabMapping[status];
    }

    // Action methods
    async navigateToGraduation(): Promise<void> {
        await this.graduationLink.click();
        // Wait for the header to ensure we're on the right page
        await this.graduationHeader.waitFor({ state: 'visible' });
    }

    async navigateToTab(status: GraduationStatus): Promise<void> {
        const tab = this.getTabByStatus(status);
        await tab.click();
    }

    // Verification methods
    async isGraduationOverviewPage(): Promise<boolean> {
        return await this.graduationHeader.isVisible();
    }

    async isGraduationDetailsModalVisible(): Promise<boolean> {
        return await this.graduationDetailsModal.isVisible();
    }

    // Actions
    private readonly PAGE_SIZE = 10; // Maximum number of students per page

    async selectRandomStudent(): Promise<void> {
        // Get count but limit to first page
        const count = await this.getStudentCount();
        if (count === 0) {
            throw new Error('No students found in the graduation list');
        }

        // Ensure we only select from visible rows on first page
        const randomIndex = Math.floor(Math.random() * Math.min(count, this.PAGE_SIZE));
        await this.studentRows.nth(randomIndex).click();
        await this.waitForModalLoad();
    }

    async getStudentCount(): Promise<number> {
        const totalCount = await this.studentRows.count();
        // Return actual count up to PAGE_SIZE
        return Math.min(totalCount, this.PAGE_SIZE);
    }

    async getGraduationDate(): Promise<string> {
        await this.waitForField(this.modalGraduationDate);
        return await this.modalGraduationDate.inputValue();
    }

    async getStudentCampus(): Promise<string> {
        await this.waitForField(this.modalCampus);
        return (await this.modalCampus.inputValue()).trim();
    }

    // Verification methods for read-only status
    async isGraduationDateReadOnly(): Promise<boolean> {
        const isDisabled = await this.modalGraduationDate.evaluate(
            (element) => element.hasAttribute('disabled') || element.hasAttribute('readonly')
        );
        return isDisabled;
    }

    async saveGraduationDetails(): Promise<void> {
        await this.saveButton.click();
    }

    async archiveGraduation(): Promise<void> {
        await this.archiveButton.click();
    }

    async deleteGraduation(): Promise<void> {
        await this.deleteButton.click();
    }

    async closeGraduationModal(): Promise<void> {
        await this.cancelButton.click();
    }

    // Helper methods
    private async waitForModalLoad(): Promise<void> {
        // Wait for modal header
        await this.graduationDetailsModal.waitFor({ state: 'visible' });

        // Additional stability wait
        await this.page.waitForTimeout(500);
    }

    async getModalStudentName(): Promise<string> {
        return await this.modalStudentName.inputValue();
    }

    private async waitForField(field: Locator): Promise<void> {
        await field.waitFor({ state: 'visible', timeout: 10000 });
        // Small wait to ensure field is stable
        await this.page.waitForTimeout(100);
    }
}
}
