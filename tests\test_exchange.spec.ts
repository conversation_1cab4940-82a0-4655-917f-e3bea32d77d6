import { expect } from '@playwright/test';
import { exchangeBotLoginProcess } from '../test-objects/login_process';
import { allure } from "allure-playwright";
import { test } from '../lib/test-logger';

test.describe('Exchange Test Suite', () => {
  test('A test to verify that Exchange is closed', async ({ page, logger }) => {
    logger.info('Starting Exchange status verification test');
    
    await allure.step("Login to the system", async () => {
      logger.debug('Attempting to log in');
      await exchangeBotLoginProcess(page);
      logger.info('Login successful');
    });

    const exchangeStatus = page.getByRole('link', { name: 'Exchange Status: Open' });
    
    try {
      if (await exchangeStatus.isVisible()) {
        logger.info('Exchange is currently open, proceeding to close it');
        await page.getByRole('link', { name: 'Exchange Status: Open' }).click();
        await expect(page.getByText('You\'re about to close Exchange')).toBeVisible();
        await page.getByRole('button', { name: 'Yes' }).click();
        logger.info('Exchange has been closed successfully');
      } else {
        logger.info('Exchange is already closed');
      }
    } catch (error) {
      logger.error('Failed to check or modify exchange status', error);
      throw error;
    }
  });

  test('A test to verify that Exchange is open', async ({ page, logger }) => {
    logger.info('Starting Exchange open status verification test');
    
    await allure.step("Login and check exchange status", async () => {
      logger.debug('Attempting to log in');
      await exchangeBotLoginProcess(page);
      logger.info('Login successful');
    });

    await page.waitForTimeout(3000);
    
    try {
      const currentStatus = await page.locator('#status').textContent();
      logger.info('Current Exchange status', { status: currentStatus });
      
      expect(currentStatus).toMatch(/Open|Closed/);
      
      if (currentStatus === 'Open') {
        logger.info('Exchange is confirmed to be open');
      } else if (currentStatus === 'Closed') {
        logger.info('Exchange is confirmed to be closed');
      }
    } catch (error) {
      logger.error('Failed to verify exchange status', error);
      throw error;
    }
  });

  test.afterEach(async ({ page }) => {
    await page.waitForTimeout(1000);
    await allure.attachment("Test Screenshot.png", await page.screenshot(), "image/png");
  });
});