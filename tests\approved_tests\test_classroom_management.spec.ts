import { test } from '../../test-objects/auth.fixture';
import * as allure from "allure-js-commons";
import { Severity } from "allure-js-commons";
import { navigateWithBotAuth, navigateWithHelperBotAuth } from '../../test-objects/login_process';
import { ClassroomManagementPage } from "../../pages/classroom-management.page";
import { expect } from '@playwright/test';

test.describe('Classroom Management Suite', () => {
    let classroomManagementPage: ClassroomManagementPage;

    test('Check that Attendance is functional', async ({ botAuth }) => {
        classroomManagementPage = new ClassroomManagementPage(botAuth.page);
        await allure.description("Testing the attendance functionality along with the filters");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await allure.step("Navigate to Classroom Management", async () => {
            await classroomManagementPage.navigateToAttendance();
        });

        await allure.step("Check if Attendance data grid is displayed using the Present Filter", async () => {
            await classroomManagementPage.checkAttendanceWithPresentFilter();
        });

        await allure.step("Check if Attendance data grid is displayed using the From & To date filter", async () => {
            await classroomManagementPage.checkAttendanceWithDateFilter();
        });

        await allure.step("Toggle from Tutor to Student Compliance", async () => {
            await classroomManagementPage.toggleToStudentCompliance();
        });

        await allure.step("Check if Attendance data grid is displayed using the Present Filter", async () => {
            await classroomManagementPage.checkAttendanceWithPresentFilter();
        });

        await allure.step("Check if Attendance data grid is displayed using the From & To date filter", async () => {
            await classroomManagementPage.checkAttendanceWithDateFilter();
        });
    });

    test('Attendance Detail is functional', async ({ helperBotAuth }) => {
        classroomManagementPage = new ClassroomManagementPage(helperBotAuth.page);
        await allure.description("Testing the Attendance Detail page functionality along with the filters");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
        });

        await allure.step("Navigate to Classroom Management & Attendance Detail", async () => {
            await classroomManagementPage.navigateToAttendance();
            await classroomManagementPage.navigateToAttendanceDetail();
        });

        await allure.step("Check if Attendance data grid is displayed using the From & To date filter", async () => {
            await classroomManagementPage.applyAttendanceDetailFilters();
        });

        await allure.step("Randomly select a Campus", async () => {
            await classroomManagementPage.selectRandomCampusForAttendanceDetail();
        });

        await allure.step("Check if Attendance Detail data grid is displayed", async () => {
            const isDisplayed = await classroomManagementPage.isAttendanceDetailDataGridDisplayed();
            if (isDisplayed) {
                console.log("Attendance Detail data grid is displayed");
            } else {
                console.error("Attendance Detail data grid is not displayed");
            }
        });
    });

    test('Student Success Overview', async ({ botAuth }) => {
        classroomManagementPage = new ClassroomManagementPage(botAuth.page);
        await allure.description("Testing the Student Success Overview page functionality");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await allure.step("Navigate to Classroom Management & Student Success Overview", async () => {
            await classroomManagementPage.navigateToStudentSuccessOverview();
        });

        await allure.step("Apply filters", async () => {
            await classroomManagementPage.applyStudentSuccessOverviewFilters();
        });

        await allure.step("Apply filter and confirm that the data grid is visible", async () => {
            const isDisplayed = await classroomManagementPage.applyStudentSuccessOverviewFiltersAndCheckDataGrid();
            if (isDisplayed) {
                console.log("Student Success Overview data grid is displayed");
            } else {
                console.error("Student Success Overview data grid is not displayed");
            }
        });
    });

    test('Student Success Detail test', async ({ helperBotAuth }) => {
        classroomManagementPage = new ClassroomManagementPage(helperBotAuth.page);
        await allure.description("Testing the Student Success Detail page functionality");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
        });

        await allure.step("Navigate to Classroom Management & Student Success Detail", async () => {
            await classroomManagementPage.navigateToStudentDetailOverview();
        });

        await allure.step("Apply filters", async () => {
            await classroomManagementPage.applyStudentSuccessDetailFilters();
        });

        await allure.step("Check if Student Success detail data grid is displayed", async () => {
            const isDisplayed = await classroomManagementPage.isBriefsDataGridDisplayed();
            if (isDisplayed) {
                console.log("Student Success detail data grid is displayed");
            } else {
                console.log("Student Success detail data grid is not displayed");
            }
        });
    });

    test('Timetable Management', async ({ botAuth }) => {
        classroomManagementPage = new ClassroomManagementPage(botAuth.page);
        await allure.description("Testing the timetable and checking that sessions populate");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await allure.step("Navigate to Classroom Management then Timetable Management", async () => {
            await classroomManagementPage.navigateToTimetables();
        });

        await allure.step("Randomly select a Campus", async () => {
            await classroomManagementPage.selectRandomCampusForTimetable();
        });

        await allure.step("Randomly select a session", async () => {
            const sessionModalVisible = await classroomManagementPage.selectRandomSession();
            if (sessionModalVisible) {
                console.log("Session Details modal is displayed");
            }
            await allure.attachment("SessionDetailsModal.png", await botAuth.page.screenshot(), "image/png");
        });

        await allure.step("Close the session", async () => {
            await classroomManagementPage.closeSessionDetails();
        });

        await allure.step("Create a blank session", async () => {
            const emptySessionModalVisible = await classroomManagementPage.createBlankSession();
            if (emptySessionModalVisible) {
                console.log("Session Details modal is displayed");
            } else {
                console.log("Session Details modal is not displayed");
            }
        });
    });

    test('Late/Resubmission Report', async ({ helperBotAuth }) => {
        classroomManagementPage = new ClassroomManagementPage(helperBotAuth.page);
        await allure.description("Testing the Late/Resubmission Report page functionality");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
        });

        await allure.step("Navigate to Classroom Management then Late/Resubmissions Report", async () => {
            await classroomManagementPage.navigateToLateResubmissionReport();
        });

        await allure.step("Apply filters using CSV data combinations", async () => {
            await classroomManagementPage.dateFilter('2025-03-01', '2025-06-30');
            await classroomManagementPage.randomLateQualification();
            await classroomManagementPage.randomAcademicYear();
            await classroomManagementPage.selectCalendarYear();
            
            // Apply the filters
            const applyButton = await classroomManagementPage.page.getByRole('button', {name: 'Apply'});
            await expect(applyButton).toBeEnabled();
            await applyButton.click();
            await classroomManagementPage.page.waitForTimeout(3000);

            // Check if data is found
            const row = await classroomManagementPage.page.locator('table tbody tr').first().isVisible();
            expect(row).toBeTruthy();
            console.log(row ? "Data found" : "No data found");
        });

        await allure.step("Check if the data grid is displayed and validate the data", async () => {
            const { totalStudentsPassed, totalStudentsPassedBeforeLate, averageCourseMarkAfterLate, averageCourseMarkBeforeLate } = await classroomManagementPage.getLateResubmissionData();
            expect.soft(Number(totalStudentsPassed)).toBeGreaterThanOrEqual(Number(totalStudentsPassedBeforeLate));
            console.log(`Total % Passing Students: ${totalStudentsPassed}, Total % Passing Students Before Late/Resubmissions: ${totalStudentsPassedBeforeLate}`);
            expect.soft(Number(averageCourseMarkAfterLate)).toBeGreaterThanOrEqual(Number(averageCourseMarkBeforeLate));
            console.log(`Average Course Mark After Late/Resubmission: ${averageCourseMarkAfterLate}, Average Course Mark Before Late/Resubmission: ${averageCourseMarkBeforeLate}`);
        });
    });

    test.afterEach(async ({ page }) => {
        await page.waitForTimeout(1000);
        await allure.attachment("Test Screenshot.png", await page.screenshot(), "image/png");
    });
});
