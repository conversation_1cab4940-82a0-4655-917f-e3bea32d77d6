pipeline {
    agent any
    
    parameters {
        choice(
            name: 'ENV',
            choices: ['prod', 'staging', 'accp'],
            description: 'Select the environment to run tests against'
        )
        choice(
            name: 'TESTS',
            choices: ['approved-tests', 'not-approved-tests'],
            description: 'Select which test suite to run'
        )
    }
    
    environment {
        ENV = "${params.ENV}"
        TESTS = "${params.TESTS}"
    }
    
    stages {
        stage('Setup') {
            steps {
                bat 'npm install'
                bat 'npx playwright install'
            }
        }
        
        stage('Run Tests') {
            steps {
                bat "npx playwright test --project=${TESTS}"
            }
        }
    }
    
    post {
        always {
            allure([
                includeProperties: false,
                jdk: '',
                results: [[path: 'allure-results']],
                reportBuildPolicy: 'ALWAYS'
            ])
            archiveArtifacts artifacts: 'allure-results/**/*', allowEmptyArchive: true
            archiveArtifacts artifacts: 'playwright-report/**/*', allowEmptyArchive: true
        }
    }
}
