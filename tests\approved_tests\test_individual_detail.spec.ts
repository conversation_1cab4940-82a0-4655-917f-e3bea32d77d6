import { test } from '../../test-objects/auth.fixture';
import { IndividualPage } from '../../pages/individual.page';
import * as allure from "allure-js-commons";
import { Severity } from "allure-js-commons";
import { expect } from '@playwright/test';
import { navigateWithBotAuth } from '../../test-objects/login_process';

test.describe('Individual Detail Tests', () => {
    let individualPage: IndividualPage;

    test('should navigate to new individual creation page', async ({ botAuth }) => {
        individualPage = new IndividualPage(botAuth.page);
        await allure.description("Testing the navigation to the new individual creation page");
        await allure.severity(Severity.BLOCKER);
        await allure.feature("Individual Detail");
        await allure.step('Navigate to login page', async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await test.step('Navigate to Individual creation', async () => {
            await individualPage.navigateToNewIndividual();
            
            const isVisible = await individualPage.isIndividualDetailPage();
            console.log(isVisible ? "Individual Detail page is visible" : "Individual Detail page is not visible");
        });
    });

    test('should be able to add a document to individual', async ({ helperBotAuth }) => {
        individualPage = new IndividualPage(helperBotAuth.page);
        await allure.description("Testing the document addition functionality for an individual");
        await allure.severity(Severity.NORMAL);
        await allure.feature("Individual Detail");

        await allure.step('Navigate to login page and wait for load', async () => {
            await navigateWithBotAuth(helperBotAuth.page);
            // Wait for navigation to complete and page to be ready
            await helperBotAuth.page.waitForURL('**/Individual_Overview/**');
            await helperBotAuth.page.waitForLoadState('domcontentloaded');
            // Add extra wait time for the page to be fully loaded
            await helperBotAuth.page.waitForTimeout(5000);
        });

        await test.step('Select a random student from the grid', async () => {
            await individualPage.selectRandomStudent();
        });

        await test.step('Navigate to Documents tab', async () => {
            await individualPage.navigateToDocumentsTab();
        });

        await test.step('Open Add Document modal', async () => {
            await individualPage.clickAddDocument();
            // Verify modal is visible
            await expect(await individualPage.isDocumentDetailsModalVisible()).toBeTruthy();
        });

        await test.step('Interact with Document Type dropdown', async () => {
            const selectedType = await individualPage.selectRandomDocumentType();
            // Verify a selection was made (selectedType should not be null or empty)
            expect(selectedType).toBeTruthy();
        });
    });
});
