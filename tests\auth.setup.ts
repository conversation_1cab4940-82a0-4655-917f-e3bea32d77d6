import { chromium } from '@playwright/test';
import dotenv from 'dotenv';

// Load environment variables
const envPath = `./env/.env.${process.env.ENV || 'accp'}`;
console.log('Loading environment from:', envPath);
dotenv.config({ path: envPath });

// Helper function for domain matching
const isDomainMatch = (cookieDomain: string, targetDomain: string) => {
    // Handle both exact matches and subdomain matches
    return cookieDomain === targetDomain ||
           cookieDomain.endsWith('.' + targetDomain) ||
           targetDomain.endsWith('.' + cookieDomain);
};

// Get environment-specific domains
const getEnvironmentDomains = () => {
    if (process.env.URL?.includes('accp')) {
        return {
            connectDomain: 'inconnect-accp-connect.stratusolvecloud.com',
            membersDomain: 'inconnect-accp-members.stratusolvecloud.com'
        };
    }
    if (process.env.URL?.includes('staging')) {
        return {
            connectDomain: 'inconnect-staging-connect.stratusolvecloud.com',
            membersDomain: 'inconnect-staging-members.stratusolvecloud.com'
        };
    }
    return {
        connectDomain: 'connect.inscape.co.za',
        membersDomain: 'members.inscape.co.za'
    };
};

const { connectDomain, membersDomain } = getEnvironmentDomains();
console.log('Environment check:', { url: process.env.URL });

console.log('Using domains:', { connectDomain, membersDomain });

async function globalSetup() {
    // Setup for bot user
    const browser = await chromium.launch();
    const context = await browser.newContext();
    const page = await context.newPage();

    try {
        console.log('Starting bot user authentication...');
        
        // Navigate to the login page
        const loginUrl = String(process.env.URL);
        console.log('Navigating to login URL:', loginUrl);
        await page.goto(loginUrl);
        
        // Wait for the login form to be ready
        await page.waitForLoadState('networkidle');
        console.log('Page loaded, proceeding with login...');

        // Fill in login credentials
        console.log('Filling login credentials...');
        await page.getByPlaceholder('Username').fill('<EMAIL>');
        await page.getByPlaceholder('Password').fill(String(process.env.BOTPASSWORD));

        // Click login button and wait for navigation
        console.log('Clicking login button...');
        await Promise.all([
            page.waitForNavigation({ waitUntil: 'networkidle' }),
            page.getByRole('button', { name: 'Login' }).click()
        ]);
        console.log('Login button clicked, navigation completed');

        // Verify initial connect domain token
        console.log('Verifying connect domain token...');
        let cookies = await context.cookies();
        console.log('Initial cookies:', cookies.map(c => ({
            name: c.name,
            domain: c.domain,
            path: c.path
        })));

        // Verify initial connect domain token
        const initialConnectToken = cookies.find(c => 
            c.name === 'ConnectSessionToken' && 
            isDomainMatch(c.domain, connectDomain)
        );

        console.log('Initial connect token check:', {
            expected_domain: connectDomain,
            found_cookies: cookies
                .filter(c => c.name === 'ConnectSessionToken')
                .map(c => ({ domain: c.domain, path: c.path }))
        });

        if (!initialConnectToken) {
            const error = new Error('Failed to establish connect domain token after login');
            error.name = 'InitialConnectTokenError';
            throw error;
        }
        console.log('Connect domain token established successfully');

        // Navigate to members site to establish cross-domain session
        const authUrl = String(process.env.AUTHURL);
        console.log('Navigating to authenticated URL:', authUrl);
        await page.goto(authUrl);
        await page.waitForLoadState('networkidle');
        console.log('Navigation to members domain completed');

        // Verify both tokens after cross-domain navigation
        console.log('Verifying both domain tokens...');
        cookies = await context.cookies();

        // Debug logging for all ConnectSessionToken cookies
        const sessionTokens = cookies.filter(c => c.name === 'ConnectSessionToken');
        console.log('All ConnectSessionToken cookies found:', sessionTokens.map(c => ({
            domain: c.domain,
            path: c.path,
            secure: c.secure,
            httpOnly: c.httpOnly
        })));

        const connectTokenCookie = sessionTokens.find(c => isDomainMatch(c.domain, connectDomain));
        const membersTokenCookie = sessionTokens.find(c => isDomainMatch(c.domain, membersDomain));

        console.log('Domain matching results:', {
            connect: {
                expected: connectDomain,
                found: connectTokenCookie?.domain,
                matches: connectTokenCookie ? 'Yes' : 'No'
            },
            members: {
                expected: membersDomain,
                found: membersTokenCookie?.domain,
                matches: membersTokenCookie ? 'Yes' : 'No'
            }
        });

        const hasConnectToken = !!connectTokenCookie;
        const hasMembersToken = !!membersTokenCookie;

        if (!hasConnectToken || !hasMembersToken) {
            const error = new Error(
                `Failed to establish authentication tokens for both domains:\n` +
                `Connect domain (${connectDomain}): ${hasConnectToken ? 'Found' : 'Missing'}\n` +
                `Members domain (${membersDomain}): ${hasMembersToken ? 'Found' : 'Missing'}`
            );
            error.name = 'TokenVerificationError';
            throw error;
        }
        console.log('Both domain tokens verified successfully');
        
        // Store authentication state
        console.log('Storing authentication state...');
        await context.storageState({ path: 'playwright/.auth/bot-user.json' });
        console.log('Bot user authentication completed successfully');

    } catch (error) {
        console.error('Bot user authentication failed:', error);
        throw error;
    } finally {
        await context.close();
        await browser.close();
    }

    // Setup for helper bot user
    const helperBrowser = await chromium.launch();
    const helperContext = await helperBrowser.newContext();
    const helperPage = await helperContext.newPage();

    try {
        console.log('Starting helper bot user authentication...');
        
        // Navigate to the login page
        const loginUrl = String(process.env.URL);
        console.log('Navigating to login URL:', loginUrl);
        await helperPage.goto(loginUrl);
        
        // Wait for the login form to be ready
        await helperPage.waitForLoadState('networkidle');
        console.log('Page loaded, proceeding with login...');

        // Fill in login credentials
        console.log('Filling login credentials...');
        await helperPage.getByPlaceholder('Username').fill('<EMAIL>');
        await helperPage.getByPlaceholder('Password').fill(String(process.env.BOTPASSWORD));

        // Click login button and wait for navigation
        console.log('Clicking login button...');
        await Promise.all([
            helperPage.waitForNavigation({ waitUntil: 'networkidle' }),
            helperPage.getByRole('button', { name: 'Login' }).click()
        ]);
        console.log('Login button clicked, navigation completed');

        // Verify initial connect domain token
        console.log('Verifying connect domain token...');
        let cookies = await helperContext.cookies();
        console.log('Initial cookies:', cookies.map(c => ({
            name: c.name,
            domain: c.domain,
            path: c.path
        })));

        const initialConnectToken = cookies.find(c => 
            c.name === 'ConnectSessionToken' && 
            isDomainMatch(c.domain, connectDomain)
        );

        console.log('Initial connect token check:', {
            expected_domain: connectDomain,
            found_cookies: cookies
                .filter(c => c.name === 'ConnectSessionToken')
                .map(c => ({ domain: c.domain, path: c.path }))
        });

        if (!initialConnectToken) {
            const error = new Error('Failed to establish connect domain token after login');
            error.name = 'InitialConnectTokenError';
            throw error;
        }
        console.log('Connect domain token established successfully');

        // Navigate to members site to establish cross-domain session
        const authUrl = String(process.env.AUTHURL);
        console.log('Navigating to authenticated URL:', authUrl);
        await helperPage.goto(authUrl);
        await helperPage.waitForLoadState('networkidle');
        console.log('Navigation to members domain completed');

        // Verify both tokens after cross-domain navigation
        console.log('Verifying both domain tokens...');
        cookies = await helperContext.cookies();

        // Debug logging for all ConnectSessionToken cookies
        const sessionTokens = cookies.filter(c => c.name === 'ConnectSessionToken');
        console.log('All ConnectSessionToken cookies found:', sessionTokens.map(c => ({
            domain: c.domain,
            path: c.path,
            secure: c.secure,
            httpOnly: c.httpOnly
        })));

        const connectTokenCookie = sessionTokens.find(c => isDomainMatch(c.domain, connectDomain));
        const membersTokenCookie = sessionTokens.find(c => isDomainMatch(c.domain, membersDomain));

        console.log('Domain matching results:', {
            connect: {
                expected: connectDomain,
                found: connectTokenCookie?.domain,
                matches: connectTokenCookie ? 'Yes' : 'No'
            },
            members: {
                expected: membersDomain,
                found: membersTokenCookie?.domain,
                matches: membersTokenCookie ? 'Yes' : 'No'
            }
        });

        const hasConnectToken = !!connectTokenCookie;
        const hasMembersToken = !!membersTokenCookie;

        if (!hasConnectToken || !hasMembersToken) {
            const error = new Error(
                `Failed to establish authentication tokens for both domains:\n` +
                `Connect domain (${connectDomain}): ${hasConnectToken ? 'Found' : 'Missing'}\n` +
                `Members domain (${membersDomain}): ${hasMembersToken ? 'Found' : 'Missing'}`
            );
            error.name = 'TokenVerificationError';
            throw error;
        }
        console.log('Both domain tokens verified successfully');
        
        // Store authentication state
        console.log('Storing authentication state...');
        await helperContext.storageState({ path: 'playwright/.auth/helper-bot-user.json' });
        console.log('Helper bot user authentication completed successfully');

    } catch (error) {
        console.error('Helper bot user authentication failed:', error);
        throw error;
    } finally {
        await helperContext.close();
        await helperBrowser.close();
    }
}

export default globalSetup;
