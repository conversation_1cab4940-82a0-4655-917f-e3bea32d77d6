import { test } from '../../test-objects/auth.fixture';
import { Severity } from "allure-js-commons";
import * as allure from "allure-js-commons";
import { PoliciesProceduresPage } from "../../pages/policies-procedures.page";
import { navigateWithBotAuth } from "../../test-objects/login_process";
import * as fs from 'fs/promises';

test.describe('Policies & Procedures Test Suite', () => {
    let policiesProceduresPage: PoliciesProceduresPage;
    let downloadedFiles: string[] = [];

    test('Download Policies & Procedures', async ({ botAuth }, testInfo) => {
        console.log('Starting Policies & Procedures test...');
        
        policiesProceduresPage = new PoliciesProceduresPage(botAuth.page);
        await allure.description("This test validates the downloading of Policies & Procedures documents");
        await allure.severity(Severity.NORMAL);
        await allure.feature("Policies & Procedures");

        await allure.step("Login and navigate to Policies & Procedures", async () => {
            await navigateWithBotAuth(botAuth.page);
            await policiesProceduresPage.navigateToPoliciesProcedures();
        });

        await allure.step("Download a random Policy document", async () => {
            const filePath = await policiesProceduresPage.downloadRandomPolicy(testInfo.outputPath());
            downloadedFiles.push(filePath);
            console.log(`File downloaded and verified: ${filePath}`);
        });

        await allure.step("Verify PDF viewer status", async () => {
            const isPDFViewerDisplayed = await policiesProceduresPage.checkPDFViewer();
            if (isPDFViewerDisplayed) {
                console.log('Document opened in PDF viewer');
            }
        });

        console.log('Policies & Procedures test completed');
    });

    test.afterEach(async ({ botAuth }, testInfo) => {
        for (const file of downloadedFiles) {
            try {
                await fs.unlink(file);
                console.log(`Cleaned up file: ${file}`);
            } catch (error) {
                console.log(`Failed to clean up file: ${file}`, error);
            }
        }
        downloadedFiles = [];

        if (testInfo.status !== testInfo.expectedStatus) {
            const screenshot = await botAuth.page.screenshot();
            await testInfo.attach('screenshot', { body: screenshot, contentType: 'image/png' });
        }
        console.log(`Test completed with status: ${testInfo.status}`);
    });
});