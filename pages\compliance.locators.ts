/**
 * Compliance module locators
 * Contains all selectors and locators for the Compliance page
 */

export const complianceLocators = {
  // Navigation
  navigation: {
    complianceLink: 'link[name="Compliance"]'
  },

  // Campus selection
  campus: {
    radioInputs: '.radio input[type="radio"]',
    radioLabel: 'label[for="{campusId}"]' // Template for dynamic campus ID
  },

  // Buttons
  buttons: {
    generateReport: 'button[name="Generate Report"]'
  },

  // Form elements
  forms: {
    combobox: 'combobox'
  }
};

/**
 * Compliance page role-based locators for better accessibility
 */
export const complianceRoleLocators = {
  complianceLink: { role: 'link', name: 'Compliance' },
  generateReportButton: { role: 'button', name: 'Generate Report' },
  categoryCell: { role: 'cell' }, // Dynamic name will be provided
  combobox: { role: 'combobox' }
};

/**
 * NLRD Data Submission categories
 */
export const nlrdCategories = [
  'Personal Information',
  'Qualification Enrolment',
  'Staff Employment Detail',
  'Student FTE File'
];

/**
 * DHET Report configurations
 */
export const dhetReports = [
  { name: 'Age Category', option: '7' },
  { name: 'Foreign Nationals Enrolment' },
  { name: 'Number of Graduates' },
  { name: 'Number of Programmes Offered by the Institution on the HEQSF' },
  { name: 'Number of Programmes Offered on the NQF & Premises of Teaching & Learning' },
  { name: 'Number of Staff Members' },
  { name: 'Student Enrolment per Programme' },
  { name: 'Student Success Rate', option: '7' }
];
