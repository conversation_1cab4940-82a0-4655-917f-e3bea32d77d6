import { test } from '../../test-objects/auth.fixture';
import { Severity } from "allure-js-commons";
import * as allure from "allure-js-commons";
import { CommunicationPage } from "../../pages/communication.page";
import { navigateWithBotAuth, navigateWithHelperBotAuth } from "../../test-objects/login_process";

test.describe('Communications Test Suite', () => {
    let communicationPage: CommunicationPage;

    test('Message Group', async ({ botAuth }) => {
        console.log('Starting Message Group test...');
        
        communicationPage = new CommunicationPage(botAuth.page);
        await allure.description("This test is to validate the creation of a message group");
        await allure.severity(Severity.NORMAL);
        await allure.feature("Communications");

        await allure.step("Login and navigate to the Message Groups page", async () => {
            await navigateWithBotAuth(botAuth.page);
            await communicationPage.navigateToMessageGroups();
        });

        await allure.step("Create a new message group", async () => {
            await communicationPage.createNewMessageGroup();
            await communicationPage.backToMessageGroups();
        });

        await allure.step("Search for a random message group", async () => {
            const randomGroup = await communicationPage.searchRandomMessageGroup();
            console.log(`Random search: ${randomGroup}`);
        });

        console.log('Message Group test completed');
    });

    test('Communication Setup for all Communications', async ({ helperBotAuth }) => {
        communicationPage = new CommunicationPage(helperBotAuth.page);
        await allure.description("This test is to validate the setup of communication for all communication types");
        await allure.severity(Severity.NORMAL);
        await allure.feature("Communications");

        await allure.step("Login and navigate to the application", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
        });

        await allure.step("Navigate to the Bulk Communications Setup page", async () => {
            await communicationPage.navigateToBulkCommunications();
        });

        await allure.step("Navigate to the Triggered Communications Setup page", async () => {
            await communicationPage.navigateToTriggeredCommunications();
            await helperBotAuth.page.waitForTimeout(3000);
        });

        await allure.step("Navigate to the Scheduled Communications Setup page", async () => {
            await communicationPage.navigateToScheduledCommunications();
            await helperBotAuth.page.waitForTimeout(3000);
        });

        await allure.step("Navigate to the Survey Communications Setup page", async () => {
            await communicationPage.navigateToSurveyCommunications();
        });
    });

    test.afterEach(async ({ page }, testInfo) => {
        if (testInfo.status !== testInfo.expectedStatus) {
            const screenshot = await page.screenshot();
            await testInfo.attach('screenshot', { body: screenshot, contentType: 'image/png' });
        }
        console.log(`Test "${testInfo.title}" completed with status: ${testInfo.status}`);
    });
});