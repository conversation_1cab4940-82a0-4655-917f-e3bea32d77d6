# INCONNECT Test Case Documentation

## Table of Contents
- [Test Case Structure](#test-case-structure)
- [Test Suites](#test-suites)
- [Writing Test Cases](#writing-test-cases)
- [Best Practices](#best-practices)

## Test Case Structure

### Basic Structure
```typescript
test.describe('Module Name Test Suite', () => {
    let modulePage: ModulePage;

    test.beforeEach(async ({ page }) => {
        modulePage = new ModulePage(page);
    });

    test('Test Case Description', async ({ page }) => {
        await allure.description("Detailed test description");
        await allure.severity(Severity.NORMAL);

        await allure.step("Step Description", async () => {
            // Test steps
        });
    });

    test.afterEach(async ({ page }) => {
        await allure.attachment("Screenshot", await page.screenshot(), "image/png");
    });
});
```

### Required Components

1. **Test Suite Description**
   - Clear module/feature identification
   - Logical grouping of related tests

2. **Page Object Initialization**
   - Create in beforeEach hook
   - Ensures clean state for each test

3. **Test Case Structure**
   - Descriptive test name
   - Allure description and severity
   - Step-by-step test flow
   - Screenshot capture on completion

## Test Suites

### 1. Academics Module
```typescript
// test_academics.spec.ts
test.describe('Academics Test Suite', () => {
    test('Qualification Creation', async ({ page }) => {
        await allure.step("Login and Navigate", async () => {
            await botLoginProcess(page);
            await academicsPage.navigateToAcademics();
        });

        await allure.step("Create Qualification", async () => {
            const qualificationsDetails = await academicsPage.createQualification();
            expect(qualificationsDetails).toBeTruthy();
        });
    });
});
```

### 2. Finance Module
```typescript
// test_finance_prod.spec.ts
test.describe('Finance Test Suite', () => {
    test('Payment Processing', async ({ page }) => {
        await allure.step("Process Payment", async () => {
            await botLoginProcess(page);
            await financePage.processPayment();
        });
    });
});
```

## Writing Test Cases

### 1. Test Case Template
```typescript
test('Action_ExpectedResult', async ({ page }) => {
    // Arrange
    await allure.description("Test objective");
    await allure.severity(Severity.NORMAL);

    // Act
    await allure.step("Setup step", async () => {
        // Setup code
    });

    await allure.step("Action step", async () => {
        // Action code
    });

    // Assert
    await allure.step("Verification step", async () => {
        // Assertion code
    });
});
```

### 2. Naming Conventions

- **Test Suite Names**: `test_[module].spec.ts`
- **Test Case Names**: `Action_ExpectedResult`
- **Step Descriptions**: Clear, action-oriented phrases

### 3. Severity Levels

```typescript
await allure.severity(Severity.BLOCKER);  // Critical functionality
await allure.severity(Severity.CRITICAL); // Core features
await allure.severity(Severity.NORMAL);   // Standard features
await allure.severity(Severity.MINOR);    // Minor features
await allure.severity(Severity.TRIVIAL);  // Visual/cosmetic
```

## Best Practices

### 1. Test Independence
- Each test should be self-contained
- Avoid dependencies between tests
- Clean up test data after execution

### 2. Assertions
```typescript
// Preferred assertions
expect(result).toBeTruthy();
expect(element).toBeVisible();
expect(text).toContain('expected value');

// Avoid multiple assertions in one test
// Instead, split into multiple focused tests
```

### 3. Error Handling
```typescript
try {
    await allure.step("Action step", async () => {
        // Test action
    });
} catch (error) {
    await allure.attachment(
        "Error Screenshot", 
        await page.screenshot(), 
        "image/png"
    );
    throw error;
}
```

### 4. Test Data Management
```typescript
// Use Faker for dynamic data
import { faker } from '@faker-js/faker';

const testData = {
    name: faker.person.fullName(),
    email: faker.internet.email(),
    phone: faker.phone.number()
};
```

### 5. Page Object Methods
```typescript
// Good: Encapsulated business logic
async createQualification() {
    await this.qualificationTab.click();
    await this.addButton.click();
    await this.fillQualificationDetails();
    return this.verifyQualificationCreated();
}

// Avoid: Exposing too many low-level actions
async clickQualificationTab() {}
async clickAddButton() {}
async enterQualificationName() {}
```

### 6. Environment Management
```typescript
// Use environment variables
const baseUrl = process.env.BASE_URL;
const credentials = {
    username: process.env.TEST_USER,
    password: process.env.TEST_PASSWORD
};
```

### Authentication in Test Cases

#### 1. Using Authentication States
```typescript
test.describe('Module Tests', () => {
    // Use bot user authentication for all tests in this describe block
    test.use({ storageState: 'playwright/.auth/bot-user.json' });

    test('Bot User Feature', async ({ page }) => {
        await allure.description("Test feature with bot user");
        // Test implementation
    });
});
```

#### 2. Multiple Authentication States
```typescript
// Bot user tests
test.describe('Bot User Features', () => {
    test.use({ storageState: 'playwright/.auth/bot-user.json' });

    test('Bot User Specific Test', async ({ page }) => {
        // Test with bot user authentication
    });
});

// Helper bot tests
test.describe('Helper Bot Features', () => {
    test.use({ storageState: 'playwright/.auth/helper-bot-user.json' });

    test('Helper Bot Specific Test', async ({ page }) => {
        // Test with helper bot authentication
    });
});
```
