import { test } from '../../test-objects/auth.fixture';
import { expect } from '@playwright/test';
import { navigateWithBotAuth, navigateWithHelperBotAuth } from '../../test-objects/login_process';
import * as allure from "allure-js-commons";
import { Severity } from "allure-js-commons";
import { EnrolmentManagementPage } from "../../pages/enrolment-management.page";

test.describe('Enrolment Management Test Suite', () => {
    let enrolmentManagementPage: EnrolmentManagementPage;

    test('Filter functionality test', async ({ botAuth }) => {
        enrolmentManagementPage = new EnrolmentManagementPage(botAuth.page);
        await allure.description("Testing the functionality of the filter along with the toggle buttons");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await allure.step("Navigate to Enrolment Management", async () => {
            await enrolmentManagementPage.navigateToEnrolmentManagement();
        });

        await allure.step("Toggle the Distance and Re-enrol buttons and filter the current enrolling year", async () => {
            await enrolmentManagementPage.filterActiveEnrolments();
        });

        await allure.step("Check if the data grid is displayed", async () => {
            const isDataGridVisible = await enrolmentManagementPage.isDataGridVisible();
            console.log(isDataGridVisible ? "Data grid is displayed" : "Data grid is not displayed");
        });
    });

    test('Document upload test', async ({ helperBotAuth }) => {
        enrolmentManagementPage = new EnrolmentManagementPage(helperBotAuth.page);
        await allure.description("Testing that a document can be uploaded to an enrolment");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
        });

        await allure.step("Navigate to Enrolment Management", async () => {
            await enrolmentManagementPage.navigateToEnrolmentManagement();
        });

        await allure.step("Toggle the Distance and Re-enrol buttons and filter the current enrolling year", async () => {
            await enrolmentManagementPage.filterActiveEnrolments();
        });

        await allure.step("Select a random enrolment", async () => {
            await enrolmentManagementPage.selectRandomEnrolment();
        });

        await allure.step("Navigate to the documents tab and upload a document", async () => {
            await enrolmentManagementPage.navigateToDocumentsTab();
            await enrolmentManagementPage.uploadDocument();

            await allure.step("Check if the upload modal is displayed", async () => {
                const isUploadModalVisible = await enrolmentManagementPage.isUploadModalVisible();
                const uploadModalText = await enrolmentManagementPage.getUploadModalText();
                console.log(isUploadModalVisible ? "Upload modal is displayed" : "Upload modal is not displayed");
                expect(uploadModalText).toBe('Add Document or Download');
            });
        });
    });

    test('Adding a Note test', async ({ botAuth }) => {
        enrolmentManagementPage = new EnrolmentManagementPage(botAuth.page);
        await allure.description("Testing that a Note can be added and deleted");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await allure.step("Navigate to Enrolment Management", async () => {
            await enrolmentManagementPage.navigateToEnrolmentManagement();
        });

        await allure.step("Toggle the Distance and Re-enrol buttons and filter the current enrolling year", async () => {
            await enrolmentManagementPage.filterActiveEnrolments();
        });

        await allure.step("Select a random enrolment", async () => {
            await enrolmentManagementPage.selectRandomEnrolment();
        });

        await allure.step("Navigate to the Notes tab and add a note", async () => {
            await enrolmentManagementPage.navigateToNotesTab();
            await enrolmentManagementPage.addNote('Test note');
        });

        await allure.step("Check if the note is added and delete the note", async () => {
            await enrolmentManagementPage.deleteNote('Test note');
            const isNoteVisible = await enrolmentManagementPage.isNoteVisible('Test note');
            console.log(isNoteVisible ? 'Note not deleted' : 'Note deleted');
        });
    });

    test('Generate a Proforma Invoice', async ({ helperBotAuth }) => {
        enrolmentManagementPage = new EnrolmentManagementPage(helperBotAuth.page);
        await allure.description("Testing that a proforma invoice can be generated");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
        });

        await allure.step("Navigate to Enrolment Management", async () => {
            await enrolmentManagementPage.navigateToEnrolmentManagement();
        });

        await allure.step("Toggle the Distance and Re-enrol buttons and filter the current enrolling year", async () => {
            await enrolmentManagementPage.filterActiveEnrolments();
        });

        await allure.step("Select a random enrolment", async () => {
            await enrolmentManagementPage.selectRandomEnrolment();
        });

        await allure.step("Generate a proforma invoice without a device fee", async () => {
            const downloadUrl = await enrolmentManagementPage.generateProformaInvoice();
            console.log(downloadUrl === 'https://documents.inscape.co.za/assets/pdf' ? 'Proforma invoice downloaded' : 'Proforma invoice not downloaded');
        });
    });

    test('Generate a Contract of Enrolment', async ({ botAuth }) => {
        enrolmentManagementPage = new EnrolmentManagementPage(botAuth.page);
        await allure.description("Testing that a Contract of Enrolment can be generated");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await allure.step("Navigate to Enrolment Management", async () => {
            await enrolmentManagementPage.navigateToEnrolmentManagement();
        });

        await allure.step("Toggle the Distance and Re-enrol buttons and filter the current enrolling year", async () => {
            await enrolmentManagementPage.filterEnrolledEnrolments();
        });

        await allure.step("Select a random enrolment", async () => {
            await enrolmentManagementPage.selectRandomEnrolment();
        });

        await allure.step("Generate a Contract of Enrolment", async () => {
            const newPageUrl = await enrolmentManagementPage.generateContractOfEnrolment();
            console.log('New page URL:', newPageUrl);
            console.log(newPageUrl.includes('Contract_Of_Enrolment') && newPageUrl.endsWith('.pdf') ? 'Contract of Enrolment PDF successfully generated and opened' : 'Contract of Enrolment PDF not generated');
        });
    });

    test('Generate an Enrolment Letter', async ({ helperBotAuth }) => {
        enrolmentManagementPage = new EnrolmentManagementPage(helperBotAuth.page);
        await allure.description("Testing that an Enrolment Letter can be generated");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
        });

        await allure.step("Navigate to Enrolment Management", async () => {
            await enrolmentManagementPage.navigateToEnrolmentManagement();
        });

        await allure.step("Toggle the Distance and Re-enrol buttons and filter the current enrolling year", async () => {
            await enrolmentManagementPage.filterEnrolledEnrolments();
        });

        await allure.step("Select a random enrolment", async () => {
            await enrolmentManagementPage.selectRandomEnrolment();
        });

        await allure.step("Generate an Enrolment Letter", async () => {
            const downloadUrl = await enrolmentManagementPage.generateEnrolmentLetter();
            console.log('Download URL:', downloadUrl);
            console.log(downloadUrl.includes('https://documents.inscape.co.za/assets/pdf') && downloadUrl.endsWith('COE.pdf') ? 'Enrolment Letter has been downloaded' : 'Enrolment Letter has not been downloaded');
        });
    });

    test('Add Note with Empty Content', async ({ botAuth }) => {
        enrolmentManagementPage = new EnrolmentManagementPage(botAuth.page);
        await allure.description("Testing adding a note with empty content");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the platform", async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await allure.step("Navigate to Enrolment Management, filter and select a random enrolment", async () => {
            await enrolmentManagementPage.navigateToEnrolmentManagement();
            await enrolmentManagementPage.filterActiveEnrolments();
            await enrolmentManagementPage.selectRandomEnrolment();
        });

        await allure.step("Attempt to add an empty note", async () => {
            await enrolmentManagementPage.navigateToNotesTab();
            await enrolmentManagementPage.addNote('');
        });

        await allure.step("Check if an empty note can be added", async () => {
            await enrolmentManagementPage.isEmptyNoteVisible('');
        });

        await allure.step("Delete the empty note", async () => {
            await enrolmentManagementPage.deleteEmptyNote('');
        });
    });

    test('Generate Course Fee Payment Plan & Milestones', async ({ helperBotAuth }) => {
        enrolmentManagementPage = new EnrolmentManagementPage(helperBotAuth.page);
        await allure.description("Testing that a Course Fee Payment Plan & Milestones can be generated");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the platform", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
        });

        await allure.step("Navigate to Enrolment Management, filter and select a random enrolment", async () => {
            await enrolmentManagementPage.navigateToEnrolmentManagement();
            await enrolmentManagementPage.filterEnrolledEnrolments();
            await enrolmentManagementPage.selectRandomEnrolment();
        });

        await allure.step("Navigate to the Change Details tab and generate the Course Fee Payment Plan & Milestones", async () => {
            await enrolmentManagementPage.navigateToChangeDetailsTab();
            await enrolmentManagementPage.generateCourseFeePaymentPlan();
            await enrolmentManagementPage.confirmPaymentPlanGeneration('idNumber');
            await enrolmentManagementPage.findSpecificPaymentPlan();
        });
    });

    test.afterEach(async ({ botAuth, helperBotAuth }) => {
        const page = botAuth?.page || helperBotAuth?.page;
        await page.waitForTimeout(1000);
        await allure.attachment("Test Screenshot.png", await page.screenshot(), "image/png");
    });
});
