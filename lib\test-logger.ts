import { test as base } from '@playwright/test';
import { Logger } from './logger';

// Extend the test fixture with logging capabilities
export const test = base.extend<{ logger: Logger }>({
    logger: async ({}, use) => {
        // Create logger instance
        const logger = Logger.getInstance();
        await use(logger);
    }
});

// Add automatic test logging
test.beforeEach(async ({ logger }, testInfo) => {
    try {
        logger.info(`Starting test: ${testInfo.title}`, {
            testFile: testInfo.file,
            project: testInfo.project.name
        });
    } catch (error) {
        console.error('Error in test.beforeEach:', error);
    }
});

test.afterEach(async ({ logger }, testInfo) => {
    try {
        const status = testInfo.status;
        const duration = testInfo.duration;
        
        if (status === 'passed') {
            logger.info(`Test completed: ${testInfo.title}`, {
                duration,
                status
            });
        } else if (status === 'failed' && testInfo.error) {
            // Convert TestInfoError to a format our logger can handle
            const error = {
                message: testInfo.error.message,
                value: testInfo.error.value,
                stack: testInfo.error.stack
            };
            logger.error(`Test failed: ${testInfo.title}`, error, {
                duration,
                status
            });
        } else {
            logger.warn(`Test ${status}: ${testInfo.title}`, {
                duration,
                status
            });
        }
    } catch (error) {
        console.error('Error in test.afterEach:', error);
    }
});

export { Logger, LogLevel } from './logger';
