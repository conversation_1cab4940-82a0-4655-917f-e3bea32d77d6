# INCONNECT Test Automation Setup and Configuration Guide

## Table of Contents
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Environment Setup](#environment-setup)
- [Configuration Files](#configuration-files)
- [Running Tests](#running-tests)
- [Reporting Setup](#reporting-setup)
- [Authentication Setup](#authentication-setup)

## Prerequisites

### Required Software
- Node.js (LTS version)
- npm (comes with Node.js)
- Visual Studio Code or similar IDE
- Git

### System Requirements
- Windows OS
- 8GB RAM minimum
- 20GB free disk space

## Installation

### 1. Clone Repository
```bash
git clone [repository-url]
cd INCONNECT-Test-Management
```

### 2. Install Dependencies
```bash
npm install
```

This will install:
- @playwright/test (v1.43.0)
- @faker-js/faker (v8.4.1)
- allure-playwright (v2.15.0)
- typescript (v5.6.3)
- Other dependencies as specified in package.json

### 3. Install Playwright Browsers
```bash
npx playwright install
```

## Environment Setup

### 1. Environment Files

Create environment files in the `env` directory:

```plaintext
env/
├── .env.accp
├── .env.prod
└── .env.test
```

### 2. Environment Variables

Each `.env` file should contain:

```plaintext
BASE_URL=https://your-environment-url
TEST_USER=your-test-user
TEST_PASSWORD=your-test-password
```

### 3. Environment Selection

Set the environment in your command line:
```bash
set ENV=accp  # For Windows
```

## Configuration Files

### 1. Playwright Configuration
File: `playwright.config.ts`

```typescript
import { defineConfig, devices } from '@playwright/test';
import { testPlanFilter } from "allure-playwright/dist/testplan.js";
import * as os from "os";
import dotenv from 'dotenv';

const envName = process.env.ENV || "accp";

dotenv.config({
  path: `./env/.env.${envName}`,
});

export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: 2,
  timeout: 250000,
  reporter: [
    ["line"],
    [
      "allure-playwright",
      {
        detail: true,
        outputFolder: "allure-results",
        suiteTitle: true,
      },
    ],
    ["html"],
  ],
  use: {
    trace: 'retain-on-failure',
    colorScheme: 'dark',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'],
      headless: true },
    },
  ],
});
```

### 2. TypeScript Configuration
File: `tsconfig.json`

```json
{
  "compilerOptions": {
    "target": "ES2021",
    "module": "commonjs",
    "moduleResolution": "node",
    "sourceMap": true,
    "outDir": "./dist",
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "skipLibCheck": true
  }
}
```

## Running Tests

### 1. Basic Test Execution
```bash
npm test
```

### 2. Run with UI Mode
```bash
npm run test:ui
```

### 3. Run in Headed Mode
```bash
npm run test:headed
```

### 4. Generate Test Report
```bash
npm run report
```

## Reporting Setup

### 1. Allure Report Configuration

Allure report configuration is defined in `playwright.config.ts`:

```typescript
reporter: [
  ["line"],
  [
    "allure-playwright",
    {
      detail: true,
      outputFolder: "allure-results",
      suiteTitle: true,
      environmentInfo: {
        framework: "Playwright",
        os_platform: os.platform(),
        os_release: os.release(),
        os_version: os.version(),
        node_version: process.version,
      }
    },
  ],
  ["html"],
],
```

### 2. Generating Reports

After test execution:
```bash
npm run report
```

This will:
1. Generate the report from results
2. Open the report in your default browser

### 3. Report Location
- Allure results: `./allure-results`
- HTML report: `./playwright-report`

## Project Structure

```plaintext
INCONNECT-Test-Management/
├── docs/
├── env/
├── lib/
├── pages/
├── test-objects/
├── tests/
├── playwright.config.ts
├── package.json
└── tsconfig.json
```

- `docs/`: Documentation files
- `env/`: Environment configuration files
- `lib/`: Shared utilities and helpers
- `pages/`: Page object definitions
- `test-objects/`: Test data and objects
- `tests/`: Test specifications

## Authentication Setup

### 1. Authentication Directory
Create the authentication storage directory:
```bash
mkdir -p playwright/.auth
```

Add to .gitignore:
```plaintext
playwright/.auth
```

### 2. Authentication Configuration
File: `tests/auth.setup.ts`

```typescript
import { test as setup } from '@playwright/test';

setup('authenticate as bot user', async ({ page }) => {
    await page.goto(process.env.URL);
    await page.getByPlaceholder('Username').fill('<EMAIL>');
    await page.getByPlaceholder('Password').fill(process.env.BOTPASSWORD);
    await page.getByRole('button', { name: 'Login' }).click();
    await page.waitForTimeout(2000);
    await page.context().storageState({ path: 'playwright/.auth/bot-user.json' });
});

setup('authenticate as helper bot user', async ({ page }) => {
    await page.goto(process.env.URL);
    await page.getByPlaceholder('Username').fill('<EMAIL>');
    await page.getByPlaceholder('Password').fill(process.env.BOTPASSWORD);
    await page.getByRole('button', { name: 'Login' }).click();
    await page.waitForTimeout(2000);
    await page.context().storageState({ path: 'playwright/.auth/helper-bot-user.json' });
});
```

### 3. Using Authentication in Tests
```typescript
test.describe('Bot User Tests', () => {
    test.use({ storageState: 'playwright/.auth/bot-user.json' });
    
    test('authenticated test', async ({ page }) => {
        // Test with bot user authentication
    });
});

test.describe('Helper Bot Tests', () => {
    test.use({ storageState: 'playwright/.auth/helper-bot-user.json' });
    
    test('authenticated test', async ({ page }) => {
        // Test with helper bot authentication
    });
});
```
