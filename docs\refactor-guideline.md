I want you to analyze and refactor a Playwright end-to-end test codebase that currently mixes page object actions and locators inside `*_objects.ts` files. The goal is to separate concerns by extracting **locators into their own `*.locators.ts` files**, and keeping **all logic and page methods inside `*.page.ts` files** using proper Page Object Model (POM) design.

### ✅ Your Task:

1. **For every `*_objects.ts` file**, extract all selectors and hardcoded locators into a new `*.locators.ts` file.
2. Create matching `*.page.ts` files (if not already present), and move the functions (like `clickAddDocumentButton`, etc.) there.
3. Refactor all existing logic to use locators imported from the new `*.locators.ts` files.
4. Ensure that each `*.page.ts` file:

   * Has a constructor with `Page` as a dependency.
   * Exposes `Locator` getters for UI elements using the locators.
   * Has clearly named async methods to represent UI interactions (e.g., `clickNewIndividual()`).
5. Replace all hard-coded strings or inline selectors in those methods with variables from the centralized locator files.

### ✅ Folder structure:

You must maintain the following folder structure:

```
/pages/
  campaign.page.ts
  campaign.locators.ts
  individual.page.ts
  individual.locators.ts
/test-objects/
  [delete all *_objects.ts files after migration]
```

### ✅ Constraints:

* Use TypeScript best practices.
* No changes to business logic or test flow — only structural refactor.
* All UI selectors should be declared as constants or pure functions in `*.locators.ts`.
* Maintain backwards compatibility — the tests should still run without breaking.
* Delete or mark old files as deprecated only **after** migration is complete.

### ✅ Sample Refactor:

If you find this in `individual_objects.ts`:

```ts
await this.page.getByRole('button', { name: 'New Individual' }).click();
```

You must convert it into:

**In `individual.locators.ts`:**

```ts
export const individualLocators = {
  newIndividualButton: '[role="button"][name="New Individual"]',
};
```

**In `individual.page.ts`:**

```ts
get newIndividualBtn() {
  return this.page.locator(individualLocators.newIndividualButton);
}
async clickNewIndividual() {
  await this.newIndividualBtn.click();
}
```

---

Once refactored, ensure the page object classes are still callable from the test files and functionally equivalent.

---