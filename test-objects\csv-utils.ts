import fs from 'fs';

export interface LateResubData {
    Qualification: string;
    'Academic Year': string;
    'Calendar Year': string;
}

export interface StudentSuccessDetailData {
    Qualification: string;
    'Calendar Year': string;
    'Year Option': string;
}

export async function readLateResubCSV(filePath: string): Promise<LateResubData[]> {
    const fileContent = await fs.promises.readFile(filePath, 'utf-8');
    const lines = fileContent.split('\n');
    return lines.slice(1)
        .filter(line => line.trim())
        .map(line => {
            const values = line.split(',');
            return {
                Qualification: values[0].trim(),
                'Academic Year': values[1].trim(),
                'Calendar Year': values[2] ? values[2].trim().replace(/\r/g, '') : '' // Remove carriage returns
            };
        });
}

export async function readStudentSuccessDetailCSV(filePath: string): Promise<StudentSuccessDetailData[]> {
    const fileContent = await fs.promises.readFile(filePath, 'utf-8');
    const lines = fileContent.split('\n');
    return lines.slice(1)
        .filter(line => line.trim())
        .map(line => {
            const values = line.split(',');
            return {
                Qualification: values[0].trim(),
                'Calendar Year': values[1].trim(),
                'Year Option': values[2].trim().replace(/\r/g, '') // Remove carriage returns
            };
        });
}
