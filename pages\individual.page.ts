import { Page, Locator } from "@playwright/test";
import { individualLocators, individualRoleLocators } from "./individual.locators";

export class IndividualPage {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    // Locator getters
    get newIndividualButton(): Locator {
        return this.page.getByRole(individualRoleLocators.newIndividualButton.role as 'button', {
            name: individualRoleLocators.newIndividualButton.name
        });
    }

    get studentGridRows(): Locator {
        return this.page.locator(individualLocators.studentGrid.table);
    }

    get documentsTab(): Locator {
        return this.page.locator(individualLocators.documents.tab);
    }

    get addDocumentButton(): Locator {
        return this.page.getByRole(individualRoleLocators.addDocumentButton.role as 'button', {
            name: individualRoleLocators.addDocumentButton.name
        });
    }

    get documentDetailsModal(): Locator {
        return this.page.locator(individualLocators.documents.modal.header);
    }

    get documentTypeLabel(): Locator {
        return this.page.getByText(individualRoleLocators.documentTypeLabel.text);
    }

    get documentTypeDropdown(): Locator {
        return this.page.locator(individualLocators.documents.modal.documentTypeDropdown);
    }

    get documentTypeCombobox(): Locator {
        return this.page.getByRole(individualRoleLocators.documentTypeCombobox.role as 'combobox')
            .filter({ has: this.documentTypeLabel });
    }

    // Action methods
    async clickNewIndividual(): Promise<void> {
        await this.newIndividualButton.click();
    }

    async navigateToNewIndividual(): Promise<void> {
        await this.clickNewIndividual();
    }

    async isIndividualDetailPage(): Promise<boolean> {
        return this.page.url().includes('/Individual_Detail');
    }

    async clickRandomStudentInGrid(): Promise<void> {
        // Wait for the page to load properly
        await this.page.waitForTimeout(4000);

        // Wait for at least one row to be visible
        await this.studentGridRows.first().waitFor({ state: 'visible', timeout: 10000 });

        // Get count of rows on current page
        const count = await this.studentGridRows.count();

        // Select a random row from the visible ones
        const randomIndex = Math.floor(Math.random() * count);
        await this.studentGridRows.nth(randomIndex).click();
    }

    async selectRandomStudent(): Promise<void> {
        await this.clickRandomStudentInGrid();
    }

    async clickDocumentsTab(): Promise<void> {
        await this.page.waitForTimeout(3000); // Wait for page transition
        await this.documentsTab.click();
        await this.page.waitForTimeout(2000); // Wait for tab content to load
    }

    async navigateToDocumentsTab(): Promise<void> {
        await this.clickDocumentsTab();
    }

    async clickAddDocumentButton(): Promise<void> {
        await this.page.waitForTimeout(3000); // Wait for tab content to stabilize
        await this.addDocumentButton.click({ timeout: 5000 });
        await this.page.waitForTimeout(2000); // Wait for modal animation
    }

    async clickAddDocument(): Promise<void> {
        await this.clickAddDocumentButton();
    }

    async isDocumentDetailsModalVisible(): Promise<boolean> {
        return await this.documentDetailsModal.isVisible();
    }

    async clickDocumentTypeDropdown(): Promise<void> {
        // Wait for the Document Type label and dropdown to be visible
        await this.documentTypeLabel.waitFor({ state: 'visible', timeout: 5000 });
        await this.documentTypeCombobox.waitFor({ state: 'visible', timeout: 5000 });
        await this.documentTypeCombobox.click({ timeout: 5000 });
    }

    async openDocumentTypeDropdown(): Promise<void> {
        await this.clickDocumentTypeDropdown();
    }

    async selectRandomDocumentType(): Promise<string> {
        // Get all options to find out how many there are
        const options = await this.documentTypeDropdown.locator('option').all();

        if (options.length === 0) {
            throw new Error('No document type options found in dropdown');
        }

        // Generate random index
        const randomIndex = Math.floor(Math.random() * options.length);

        // Get the text of the option we'll select (for returning)
        const selectedOption = options[randomIndex];
        const optionText = await selectedOption.textContent();
        console.log(`Selected Document Type: ${optionText}`);

        // Use selectOption with the value attribute
        await this.documentTypeDropdown.selectOption({ index: randomIndex });

        return optionText?.trim() ?? '';
    }
}
