import { test } from '../../test-objects/auth.fixture';
import { Severity } from "allure-js-commons";
import * as allure from "allure-js-commons";
import { LeadManagementPage } from '../../pages/leadmanagement.page';
import { navigateWithBotAuth } from '../../test-objects/login_process';

test.describe('Lead Management Test Suite', () => {
    let leadManagementPage: LeadManagementPage;

    test('Lead Management Process', async ({ botAuth }) => {
        leadManagementPage = new LeadManagementPage(botAuth.page);

        await allure.description("Lead Management Test. This test will navigate to the Lead Management page, " +
            "apply filters, select a random lead, navigate to interaction history, add a tag, and navigate to other tabs if " +
            "available.");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await allure.step("Navigate to Lead Management", async () => {
            await leadManagementPage.navigateToLeadManagement();
        });

        await allure.step("Reset filters", async () => {
            await leadManagementPage.resetFilters();
        });

        await allure.step("Apply filters", async () => {
            await leadManagementPage.applyFilters();
        });

        await allure.step("Select a random lead", async () => {
            await leadManagementPage.selectRandomLead();
        });

        await allure.step("Navigate to Interaction History", async () => {
            await leadManagementPage.navigateToInteractionHistory();
        });

        await allure.step("Navigate to Lead Tags", async () => {
            await leadManagementPage.navigateToLeadTags();
        });

        await allure.step("Add a tag from the dropdown. Check if the tag is visible", async () => {
            await leadManagementPage.addTag();
        });

        await allure.step("Navigate to Other tab", async () => {
            await leadManagementPage.navigateToOtherTab();
        });
    });

    test.afterEach(async ({ page }, testInfo) => {
        if (testInfo.status !== testInfo.expectedStatus) {
            // Take screenshot only on failure
            const screenshot = await page.screenshot();
            await testInfo.attach('screenshot', { body: screenshot, contentType: 'image/png' });
        }
    });
});