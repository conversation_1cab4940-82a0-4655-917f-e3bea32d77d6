import { test } from '../../test-objects/auth.fixture';
import { AnalyticsPage } from '../../pages/analytics.page';
import * as allure from "allure-js-commons";
import { Severity } from "allure-js-commons";
import { navigateWithBotAuth, navigateWithHelperBotAuth } from '../../test-objects/login_process';

test.describe('Analytics Test Suite', () => {
    let analyticsPage: AnalyticsPage;

    test('Create a new Report Category', async ({ botAuth }) => {
        analyticsPage = new AnalyticsPage(botAuth.page);
        
        await allure.description("Testing the creation of a new Report Category");
        await allure.severity(Severity.NORMAL);
        await allure.feature("Analytics");

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await allure.step("Navigate to Report Manager", async () => {
            await analyticsPage.navigateToReportManager();
        });

        await allure.step("Click on 'New Report Category' button", async () => {
            await analyticsPage.clickNewReportCategory();
        });

        await allure.step("Report category input is visible", async () => {
            const isVisible = await analyticsPage.isReportCategoryInputVisible();
            console.log(isVisible ? "Report Category input is visible" : "Report Category input is not visible");
        });
    });

    test('Create a new Report', async ({ helperBotAuth }) => {
        analyticsPage = new AnalyticsPage(helperBotAuth.page);
        
        await allure.description("Testing the creation of a new Report");
        await allure.severity(Severity.NORMAL);
        await allure.feature("Analytics");

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
        });

        await allure.step("Navigate to Report Manager", async () => {
            await analyticsPage.navigateToReportManager();
        });

        await allure.step("Click the New Report button", async () => {
            await analyticsPage.clickNewReport();
        });

        await allure.step("Report Name input is visible", async () => {
            const isVisible = await analyticsPage.isReportNameInputVisible();
            console.log(isVisible ? "Report Name input is visible" : "Report Name input is not visible");
        });
    });

    test('View an Analytics Data Report', async ({ botAuth }) => {
        analyticsPage = new AnalyticsPage(botAuth.page);
        
        await allure.description("Testing the viewing of reports");
        await allure.severity(Severity.NORMAL);
        await allure.feature("Analytics");

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await allure.step("Navigate to Data Analytics", async () => {
            await analyticsPage.navigateToDataAnalytics();
        });

        await allure.step("Click on a random report", async () => {
            await analyticsPage.clickRandomReport();
        });

        await allure.step("Report data grid is visible", async () => {
            const isVisible = await analyticsPage.isReportDataGridVisible();
            console.log(isVisible ? "Report data grid is visible" : "Report data grid is not visible");
        });
    });

    test('Test the Report Overview Filter', async ({ helperBotAuth }) => {
        analyticsPage = new AnalyticsPage(helperBotAuth.page);
        
        await allure.description("Testing the Report Overview Filter");
        await allure.severity(Severity.NORMAL);
        await allure.feature("Analytics");

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
        });

        await allure.step("Navigate to Data Analytics", async () => {
            await analyticsPage.navigateToDataAnalytics();
        });

        await allure.step("Look for a random report name and search for the selected report", async () => {
            await analyticsPage.searchRandomReport();
        });

        await allure.step("Report data grid is visible", async () => {
            const randomSearch = await analyticsPage.searchRandomReport();
            await analyticsPage.findRowWithRandomSearch(randomSearch);
            const isVisible = await analyticsPage.isReportDataGridRowVisible();
            console.log(isVisible ? "Report data grid is visible" : "Report data grid is not visible");
        });
    });

    test.afterEach(async ({ page }, testInfo) => {
        if (testInfo.status !== testInfo.expectedStatus) {
            // Take screenshot only on failure
            const screenshot = await page.screenshot();
            await testInfo.attach('screenshot', { body: screenshot, contentType: 'image/png' });
        }
    });
});