// This file contains graduation-related test objects and data

export enum GraduationStatus {
    IN_PROGRESS = 'In Progress',
    VERIFIED = 'Verified',
    READY_FOR_GRADUATION = 'Ready for Graduation',
    ARCHIVED = 'Archived',
    FAILED = 'Failed'
}

export interface GraduationDetails {
    studentName: string;
    studentNumber: string;
    idNumber: string;
    graduationDate: string;
    confirmedFullName: string;
    campus: string;
    certificateNumber: string;
    course: string;
}

// Selectors that could be reused across different tests
export const GraduationSelectors = {
    dataGrid: {
        activeTabTable: '.table-responsive table.table',
        studentRow: 'tbody tr',
        studentName: 'td:first-child',
        specialization: 'td:nth-child(2)',
        campus: 'td:nth-child(3)'
    },
    tabs: {
        inProgress: 'a[href="#inprogress"]',
        verified: 'a[href="#infoverified"]',
        readyForGraduation: 'a[href="#graduationdocuments"]',
        archived: 'a[href="#archived"]',
        failed: 'a[href="#failed"]'
    },
    modal: {
        header: 'h4:has-text("Graduation Details")',
        studentName: '#c39',
        studentNumber: '#c41',
        idNumber: '#c42',
        campus: '#c46',
        graduationDate: '#c29',
        saveButton: '#c53',
        archiveButton: '#c54',
        deleteButton: '#c55',
        cancelButton: 'button:has-text("Cancel")'
    }
};
