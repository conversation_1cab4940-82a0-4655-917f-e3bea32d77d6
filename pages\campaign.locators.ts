/**
 * Campaign module locators
 * Contains all selectors and locators for the Campaign page
 */

export const campaignLocators = {
  // Navigation
  navigation: {
    crmMenu: 'text=CRM',
    campaignsMenu: 'text=Campaigns'
  },

  // Campaign tiles and grid
  campaignTiles: '.panel.panel-default.tile.tile-hover',
  campaignDataGrid: 'div#CampaignDataGrid table tbody tr',

  // Buttons
  buttons: {
    newLead: 'button#c14',
    newCampaign: '//*[@id="c1"]',
    newSession: 'button[name=" New Session"]',
    importLeads: '//div[@id="crm_leads"]/div/div[2]/div[@class="row"]/div[2]/span/button[@name="c15"]'
  },

  // Modals
  modals: {
    leadDetails: '#LeadModalLabel >> text=Lead Details',
    campaignDetails: 'h4#CampaignDetailsModalLabel',
    newSession: 'h4#LeadCaptureSessionModalLabel',
    fileUpload: '//div[@id="importModal"]/div[@class="modal-dialog modal-lg"]//div[@class="modal-header"]'
  },

  // Links
  links: {
    captureSessions: 'link[name="Capture Sessions"]'
  },

  // Lead import selectors
  leadImport: {
    leadGenerator: '//form[@id="CampaignCategory_Detail"]/span[1]/div/div[@role="dialog"]//ul/li[1]',
    leadManager: '//form[@id="CampaignCategory_Detail"]/span[2]/div/div[@role="dialog"]//ul/li[1]'
  }
};

/**
 * Campaign page role-based locators for better accessibility
 */
export const campaignRoleLocators = {
  captureSessions: { role: 'link', name: 'Capture Sessions' },
  newSession: { role: 'button', name: ' New Session' }
};
