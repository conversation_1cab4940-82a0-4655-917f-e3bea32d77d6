import { Page, expect, Locator } from '@playwright/test';
import { enrolmentManagementLocators, enrolmentManagementRoleLocators, enrolmentUrls } from './enrolment-management.locators';
import * as allure from "allure-js-commons";

export class EnrolmentManagementPage {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    // Locator getters
    get enrolmentsMenu(): Locator {
        return this.page.locator(enrolmentManagementLocators.navigation.enrolmentsMenu);
    }

    get enrolmentManagementLink(): Locator {
        return this.page.locator(enrolmentManagementLocators.navigation.enrolmentManagementLink);
    }

    get distanceToggle(): Locator {
        return this.page.locator(enrolmentManagementLocators.filters.distanceToggle);
    }

    get reEnrolToggle(): Locator {
        return this.page.locator(enrolmentManagementLocators.filters.reEnrolToggle);
    }

    get yearFilterButton(): Locator {
        return this.page.locator(enrolmentManagementLocators.filters.yearFilterButton);
    }

    get customSelectDropdown(): Locator {
        return this.page.locator(enrolmentManagementLocators.filters.customSelectDropdown);
    }

    get yearDropdownMenu(): Locator {
        return this.page.locator(enrolmentManagementLocators.filters.yearDropdownMenu);
    }

    get allCheckboxSelector(): Locator {
        return this.page.locator(enrolmentManagementLocators.filters.allCheckboxSelector);
    }

    get applyButton(): Locator {
        return this.page.getByRole(enrolmentManagementRoleLocators.applyButton.role, {
            name: enrolmentManagementRoleLocators.applyButton.name
        });
    }

    get activeStatusButton(): Locator {
        return this.page.locator(enrolmentManagementLocators.filters.activeStatusButton);
    }

    get enrolledStatusButton(): Locator {
        return this.page.locator(enrolmentManagementLocators.filters.enrolledStatusButton);
    }

    get enrolmentDataGrid(): Locator {
        return this.page.locator(enrolmentManagementLocators.dataGrid.dataGridSelector);
    }

    get documentsTab(): Locator {
        return this.page.locator(enrolmentManagementLocators.tabs.documentsTab);
    }

    get uploadButtons(): Locator {
        return this.page.locator(enrolmentManagementLocators.documents.uploadButtons);
    }

    get uploadModal(): Locator {
        return this.page.locator(enrolmentManagementLocators.documents.uploadModal);
    }

    get notesTab(): Locator {
        return this.page.getByRole(enrolmentManagementRoleLocators.notesTab.role, {
            name: enrolmentManagementRoleLocators.notesTab.name
        });
    }

    get changeDetailsTab(): Locator {
        return this.page.getByRole(enrolmentManagementRoleLocators.changeDetailsTab.role, {
            name: enrolmentManagementRoleLocators.changeDetailsTab.name
        });
    }

    get newNoteButton(): Locator {
        return this.page.getByRole(enrolmentManagementRoleLocators.newNoteButton.role, {
            name: enrolmentManagementRoleLocators.newNoteButton.name
        });
    }

    get noteInput(): Locator {
        return this.page.getByLabel(enrolmentManagementRoleLocators.noteInput.label, {
            exact: enrolmentManagementRoleLocators.noteInput.exact
        });
    }

    get generateProformaButton(): Locator {
        return this.page.getByRole(enrolmentManagementRoleLocators.generateProformaButton.role, {
            name: enrolmentManagementRoleLocators.generateProformaButton.name
        });
    }

    get generateCOEButton(): Locator {
        return this.page.getByRole(enrolmentManagementRoleLocators.generateCOEButton.role, {
            name: enrolmentManagementRoleLocators.generateCOEButton.name
        });
    }

    get enrolmentLetterButton(): Locator {
        return this.page.getByRole(enrolmentManagementRoleLocators.enrolmentLetterButton.role, {
            name: enrolmentManagementRoleLocators.enrolmentLetterButton.name
        });
    }

    get contractTypeDropdown(): Locator {
        return this.page.locator(enrolmentManagementLocators.payment.contractTypeDropdown);
    }

    get generatePaymentPlanButton(): Locator {
        return this.page.getByRole(enrolmentManagementRoleLocators.generatePaymentPlanButton.role, {
            name: enrolmentManagementRoleLocators.generatePaymentPlanButton.name
        });
    }

    get confirmButton(): Locator {
        return this.page.locator(enrolmentManagementLocators.payment.confirmButton);
    }

    get okButton(): Locator {
        return this.page.locator(enrolmentManagementLocators.payment.okButton);
    }

    get individualLink(): Locator {
        return this.page.locator(enrolmentManagementLocators.search.individualLink);
    }

    get searchBox(): Locator {
        return this.page.locator(enrolmentManagementLocators.search.searchBox);
    }

    get studentDataGrid(): Locator {
        return this.page.locator(enrolmentManagementLocators.search.studentDataGrid);
    }

    get financesTab(): Locator {
        return this.page.locator(enrolmentManagementLocators.tabs.financesTab);
    }

    get paymentPlansTab(): Locator {
        return this.page.getByRole(enrolmentManagementRoleLocators.paymentPlansTab.role, {
            name: enrolmentManagementRoleLocators.paymentPlansTab.name
        });
    }

    get paymentPlansDataGrid(): Locator {
        return this.page.locator(enrolmentManagementLocators.paymentPlans.dataGrid);
    }

    // Helper method to get year checkbox by year
    getYearCheckbox(year: string): Locator {
        return this.page.locator(`${enrolmentManagementLocators.filters.yearCheckbox}:has-text("${year}")`);
    }

    // Helper method to get note cell by text
    getNoteCell(noteText: string): Locator {
        return this.page.getByRole(enrolmentManagementRoleLocators.noteCell.role, { name: noteText });
    }

    // Action methods
    async navigateToEnrolmentManagement(): Promise<void> {
        await this.enrolmentsMenu.click();
        await this.enrolmentManagementLink.click();
        await this.page.waitForLoadState('networkidle');
    }

    async filterActiveEnrolments(): Promise<void> {
        await allure.step("Toggle the Distance and Re-enrol buttons", async () => {
            await this.page.waitForTimeout(2000);
            await this.distanceToggle.click();
            await this.reEnrolToggle.click();
            await this.page.waitForTimeout(5000);
        });

        await allure.step("Filter the data grid", async () => {
            await this.yearFilterButton.click();
            const eligibleYear = (new Date().getFullYear()).toString();
            console.log("Enrolling Year: " + eligibleYear);
            await this.customSelectDropdown.click();
            await this.yearDropdownMenu.click();
            await this.allCheckboxSelector.click();
            const checkbox = this.getYearCheckbox(eligibleYear);
            await checkbox.click();
            await allure.attachment("EnrollingYearSelection.png", await this.page.screenshot(), {
                contentType: "image/png",
            });
            await this.yearFilterButton.click();
            await this.applyButton.click();
            await this.page.waitForTimeout(4000);
            await this.activeStatusButton.click();
            await this.page.waitForTimeout(5000);
        });
    }

    async filterEnrolledEnrolments(): Promise<void> {
        await allure.step("Toggle the Distance and Re-enrol buttons", async () => {
            await this.page.waitForTimeout(2000);
            await this.distanceToggle.click();
            await this.reEnrolToggle.click();
            await this.page.waitForTimeout(5000);
        });

        await allure.step("Filter the data grid", async () => {
            await this.yearFilterButton.click();
            const eligibleYear = (new Date().getFullYear() + 1).toString();
            console.log("Enrolling Year: " + eligibleYear);
            await this.customSelectDropdown.click();
            await this.yearDropdownMenu.click();
            await this.allCheckboxSelector.click();
            const checkbox = this.getYearCheckbox(eligibleYear);
            await checkbox.click();
            await allure.attachment("EnrollingYearSelection.png", await this.page.screenshot(), {
                contentType: "image/png",
            });
            await this.yearFilterButton.click();
            await this.applyButton.click();
            await this.page.waitForTimeout(4000);
            await this.enrolledStatusButton.click();
            await this.page.waitForTimeout(5000);
        });
    }

    async selectRandomEnrolment(): Promise<void> {
        const randomRow = Math.floor(Math.random() * 20) + 1;
        const enrolmentRowSelector = `${enrolmentManagementLocators.dataGrid.enrolmentRows}[${randomRow}]`;
        await this.page.waitForSelector(enrolmentRowSelector);
        const enrolmentRow = this.page.locator(enrolmentRowSelector);
        if (await enrolmentRow.isVisible()) {
            await enrolmentRow.click();
        } else {
            console.log(`Row ${randomRow} is not available in the data grid`);
        }
    }

    async isDataGridVisible(): Promise<boolean> {
        return await this.enrolmentDataGrid.isVisible();
    }

    async navigateToDocumentsTab(): Promise<void> {
        await this.documentsTab.click();
        await this.page.waitForTimeout(3000);
    }

    async uploadDocument(): Promise<void> {
        const uploadButtons = await this.uploadButtons.all();
        if (uploadButtons.length > 0) {
            const randomButtonIndex = Math.floor(Math.random() * uploadButtons.length);
            const randomButton = uploadButtons[randomButtonIndex];
            await randomButton.click();
        } else {
            console.error("No upload buttons found");
        }
        await this.page.waitForTimeout(4000);
    }

    async isUploadModalVisible(): Promise<boolean> {
        return await this.uploadModal.isVisible();
    }

    async getUploadModalText(): Promise<string | null> {
        return await this.uploadModal.textContent();
    }

    async navigateToNotesTab(): Promise<void> {
        await this.notesTab.click();
    }

    async navigateToChangeDetailsTab(): Promise<void> {
        await this.changeDetailsTab.click();
    }

    async addNote(noteText: string): Promise<void> {
        await this.newNoteButton.click();
        await this.noteInput.fill(noteText);
        await this.page.getByLabel('Enrolment Note Details').getByText('Save').click();
    }

    async deleteNote(noteText: string): Promise<void> {
        await this.getNoteCell(noteText).click();
        await this.page.getByLabel('Enrolment Note Details').getByText('Delete').click();
        await this.page.getByRole(enrolmentManagementRoleLocators.yesButton.role, {
            name: enrolmentManagementRoleLocators.yesButton.name
        }).click();
    }

    async deleteEmptyNote(noteText: string): Promise<void> {
        await this.getNoteCell(noteText).nth(13).click();
        await this.page.getByLabel('Enrolment Note Details').getByText('Delete').click();
        await this.page.getByRole(enrolmentManagementRoleLocators.yesButton.role, {
            name: enrolmentManagementRoleLocators.yesButton.name
        }).click();
    }

    async isEmptyNoteVisible(noteText: string): Promise<void> {
        const emptyNote = this.getNoteCell(noteText);
        const isEmptyNoteVisible = await emptyNote.count() > 0;
        if (isEmptyNoteVisible) {
            console.log("Empty Note is visible. An empty note can be added.");
        } else {
            console.log("Empty Note is not visible. An empty note cannot be added.");
        }
    }

    async isNoteVisible(noteText: string): Promise<boolean> {
        return await this.getNoteCell(noteText).isVisible();
    }

    async generateProformaInvoice(): Promise<string> {
        await this.generateProformaButton.click();
        await this.page.locator(enrolmentManagementLocators.payment.generateProformaInvoiceText).click();
        await this.page.locator(enrolmentManagementLocators.payment.paymentTypeSelect).selectOption('1');
        await this.page.locator(enrolmentManagementLocators.payment.paymentMethodSelect).selectOption('4');
        const downloadPromise = this.page.waitForEvent('download');
        await this.page.getByText(enrolmentManagementRoleLocators.downloadText.text, {
            exact: enrolmentManagementRoleLocators.downloadText.exact
        }).click();
        const download = await downloadPromise;
        return download.url();
    }

    async generateContractOfEnrolment(): Promise<string> {
        const newPagePromise = this.page.context().waitForEvent('page');
        await this.generateCOEButton.click();
        const newPage = await newPagePromise;
        await newPage.waitForLoadState('networkidle');
        const newPageUrl = newPage.url();
        await newPage.close();
        return newPageUrl;
    }

    async generateEnrolmentLetter(): Promise<string> {
        await this.enrolmentLetterButton.click();
        const downloadPromise = this.page.waitForEvent('download');
        const download = await downloadPromise;
        return download.url();
    }

    async applyFilter(eligibleYear: string): Promise<void> {
        console.log("Enrolling Year: " + eligibleYear);
        const checkbox = this.getYearCheckbox(eligibleYear);
        await checkbox.click();
    }

    async navigateWithoutLogin(): Promise<void> {
        const env = process.env.ENV === 'accp' ? 'accp' : 'prod';
        const enrolmentManagementUrl = enrolmentUrls.enrolmentManagement[env];
        const loginUrl = enrolmentUrls.login[env];

        await this.page.goto(enrolmentManagementUrl);
        await this.page.waitForTimeout(1000);
        expect(this.page.url()).toContain(loginUrl);
        console.log(loginUrl);
    }

    async generateCourseFeePaymentPlan(): Promise<void> {
        const currentOption = await this.contractTypeDropdown.locator('option:checked').getAttribute('value');

        // If Early Bird (value="1") is selected, switch to Standard (value="3")
        // If Standard (value="3") is selected, switch to Early Bird (value="1")
        const newValue = currentOption === '1' ? '3' : '1';

        await this.contractTypeDropdown.selectOption(newValue);
        await this.page.waitForTimeout(1000); // Wait for selection to take effect

        const selectedOption = await this.contractTypeDropdown.locator('option:checked').innerText();
        console.log("Selected option: " + selectedOption);
        expect(selectedOption).not.toBe('--Please Select--');

        await this.page.waitForTimeout(2000);
        await this.generatePaymentPlanButton.first().click();

        await expect(this.confirmButton).toBeVisible();
        await this.confirmButton.click();
        await this.page.waitForTimeout(2000);

        const successMessage = await this.page.locator(enrolmentManagementLocators.messages.successTitle).textContent();
        expect(successMessage).toBe('Success!');
        const contentMessage = await this.page.locator(enrolmentManagementLocators.messages.successContent).textContent();
        expect(contentMessage).toBe('Successfully generated payment plan and associated milestones');

        await expect(this.okButton).toBeVisible();
        await this.okButton.click();
    }

    async confirmPaymentPlanGeneration(extractType: 'firstName' | 'idNumber' = 'firstName'): Promise<void> {
        const studentNameElement = this.page.locator(enrolmentManagementLocators.search.studentNameSelector);
        await expect(studentNameElement).toBeVisible();

        const fullText = await studentNameElement.innerText();
        console.log(`Full text: ${fullText}`);

        // Extract either first name or ID number based on the parameter
        let extractedText: string;
        if (extractType === 'firstName') {
            // Format is "LastName FirstName (ID)" - get the first name
            const namePart = fullText.split('(')[0].trim();  // Get everything before the ID
            const names = namePart.split(' ');
            extractedText = names[1] || fullText; // Get the second word (first name)
        } else {
            // Extract ID number between parentheses
            const idMatch = fullText.match(/\((\d+)\)/);
            extractedText = idMatch ? idMatch[1] : fullText;
        }

        console.log(`Extracted ${extractType}: ${extractedText}`);

        // Rest of the method remains the same
        await this.page.evaluate((text) => {
            navigator.clipboard.writeText(text);
        }, extractedText);

        await this.individualLink.click();
        await this.page.waitForTimeout(2000);

        await this.searchBox.fill(extractedText);
        await this.searchBox.press('Enter');

        await this.studentDataGrid.waitFor({ state: 'visible' });
        const studentRow = this.page.locator(enrolmentManagementLocators.search.studentRow)
            .filter({ hasText: extractedText });
        await expect(studentRow).toBeVisible();
        await studentRow.click();
    }

    async findSpecificPaymentPlan(): Promise<void> {
        // Click on the "Finances" tab
        await this.financesTab.click();
        await this.page.waitForTimeout(2000);
        // Wait for the relevant financial information to load
        await this.paymentPlansTab.click();
        await this.paymentPlansDataGrid.waitFor({ state: 'visible' });
        const rows = await this.page.locator('tr').all();
        const paymentPlanRow = await Promise.all(
            rows.map(async (row) => {
                const text = await row.textContent();
                return text &&
                       (text.includes('Early Bird') || text.includes('Standard')) &&
                       text.includes('2025') ? row : null;
            })
        );
        const filteredRow = paymentPlanRow.find(row => row !== null);
        if (filteredRow) {
            const locator = this.page.locator(await filteredRow.evaluate(el => {
                return CSS.escape(el.tagName.toLowerCase()) +
                       Array.from(el.attributes)
                           .map(attr => `[${attr.name}="${attr.value}"]`)
                           .join('');
            }));
            await expect(locator).toBeVisible();
            console.log("Payment plan found and is visible");
        } else {
            console.log("No matching payment plan found");
        }
    }
}
}