import { execSync } from 'child_process';
import { existsSync, readdirSync, statSync, unlinkSync } from 'fs';
import { join } from 'path';

async function globalTeardown() {
    console.log('Starting global teardown...');
    const startTime = Date.now();

    try {
        // Clean up only Playwright browser processes
        if (process.platform === 'win32') {
            console.log('Running on Windows, checking for Playwright Chrome processes...');
            try {
                const stdout = execSync('wmic process where "name=\'chrome.exe\' and commandline like \'%--remote-debugging-port%\'" get processid').toString();
                const pids = stdout.split('\n')
                    .map(line => line.trim())
                    .filter(line => line && line !== 'ProcessId');

                console.log(`Found ${pids.length} Playwright Chrome processes to clean up`);
                
                for (const pid of pids) {
                    try {
                        execSync(`taskkill /F /PID ${pid}`);
                        console.log(`Successfully terminated process ${pid}`);
                        // Give time for process cleanup
                        await new Promise(resolve => setTimeout(resolve, 500));
                    } catch (killErr) {
                        console.log(`Error killing process ${pid}:`, killErr);
                    }
                }
            } catch (err) {
                console.log('Error finding Playwright Chrome processes:', err);
            }
        }

        // Wait for processes to fully terminate
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Clean up test artifacts older than 24 hours
        const cleanupDirectories = [
            './test-results/screenshots',
            './test-results/videos',
            './test-results/traces'
        ];

        console.log('Starting cleanup of old test artifacts...');
        const ONE_DAY = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
        const now = Date.now();

        for (const dir of cleanupDirectories) {
            if (existsSync(dir)) {
                const files = readdirSync(dir);
                console.log(`Checking ${files.length} files in ${dir}`);
                
                for (const file of files) {
                    try {
                        const filePath = join(dir, file);
                        const stats = statSync(filePath);
                        
                        if (now - stats.mtime.getTime() > ONE_DAY) {
                            try {
                                unlinkSync(filePath);
                                console.log(`Cleaned up old test artifact: ${filePath}`);
                            } catch (err) {
                                console.error(`Error cleaning up ${filePath}:`, err);
                            }
                        }
                    } catch (err) {
                        console.log(`Skipping file due to access error: ${file}`);
                        continue;
                    }
                }
            } else {
                console.log(`Directory ${dir} does not exist, skipping cleanup`);
            }
        }

        console.log('Global teardown completed successfully');
    } catch (error) {
        console.error('Error in global teardown:', error);
    } finally {
        const endTime = Date.now();
        console.log(`Global teardown took ${endTime - startTime}ms to complete`);
    }
}

export default globalTeardown; 