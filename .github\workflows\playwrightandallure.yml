name: Playwright Tests with Allure Report
on:
  schedule:
    - cron: '0 19 * * 3'
  workflow_dispatch:
    branches:
      - master

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    strategy:
      matrix:
        environment: [accp]
    
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
      
      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.OS }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.OS }}-node-

      - name: Cache Playwright browsers
        uses: actions/cache@v3
        with:
          path: ~/.cache/ms-playwright
          key: ${{ runner.OS }}-playwright-${{ hashFiles('**/package-lock.json') }}

      - name: System information
        run: |
          echo "OS version: $(lsb_release -d)"
          echo "Available disk space: $(df -h)"
          echo "Available memory: $(free -h)"

      - name: Install dependencies
        run: npm ci --verbose

      - name: Clean Playwright cache
        run: |
          rm -rf ~/.cache/ms-playwright
          rm -rf /home/<USER>/.cache/ms-playwright

      
      - name: Install Playwright Browsers (Attempt 1)
        id: install_playwright_1
        continue-on-error: true
        run: npx playwright install --with-deps

      - name: Install Playwright Browsers (Attempt 2)
        id: install_playwright_2
        if: steps.install_playwright_1.outcome == 'failure'
        continue-on-error: true
        run: npx playwright install --with-deps

      - name: Install Playwright Browsers (Attempt 3)
        id: install_playwright_3
        if: steps.install_playwright_2.outcome == 'failure'
        run: npx playwright install --with-deps

      - name: Verify Playwright installation
        run: |
          npx playwright --version
          ls -R ~/.cache/ms-playwright

      - name: Install Allure Playwright
        run: npm i allure-playwright
        
      - name: Install Playwright again
        run: npx playwright install

      - name: Run Playwright tests (Attempt 1)
        id: run_tests_1
        continue-on-error: true
        env:
          ENV: ${{ matrix.environment }}
          URL: ${{ secrets.ACCP_URL }}
          EXCHANGEURL: ${{ secrets.ACCP_EXCHANGE_URL }}
          PASSWORD: ${{ secrets.TEST_PASSWORD }}
          BOTPASSWORD: ${{ secrets.ACCP_BOT_PASSWORD }}
        run: npx playwright test --reporter=line,allure-playwright

      - name: Run Playwright tests (Attempt 2)
        id: run_tests_2
        if: steps.run_tests_1.outcome == 'failure'
        env:
          ENV: ${{ matrix.environment }}
          URL: ${{ secrets.ACCP_URL }}
          EXCHANGEURL: ${{ secrets.ACCP_EXCHANGE_URL }}
          PASSWORD: ${{ secrets.TEST_PASSWORD }}
          BOTPASSWORD: ${{ secrets.ACCP_BOT_PASSWORD }}
        run: npx playwright test --reporter=line,allure-playwright
      - name: Get Allure history
        uses: actions/checkout@v4
        if: always()
        continue-on-error: true
        with:
          ref: gh-pages
          path: gh-pages
      - name: Generate Allure Report
        uses: simple-elf/allure-report-action@master
        if: always()
        with:
          allure_results: allure-results
          allure_report: allure-report
          keep_reports: 20

      - name: Upload Allure Report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: allure-report-accp
          path: allure-report/
          retention-days: 90

      - name: Upload Playwright Test Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report-${{ matrix.environment }}
          path: playwright-report/
          retention-days: 90
