import { test } from '../../test-objects/auth.fixture';
import { expect } from '@playwright/test';
import { navigateWithBotAuth, navigateWithHelperBotAuth } from "../../test-objects/login_process";
import {AcademicsPage} from "../../pages/academics.page";
import * as allure from "allure-js-commons";
import {Severity} from "allure-js-commons";

test.describe('Academics Test Suite', () => {
    let academicsPage: AcademicsPage;

    test('Qualification Creation', async ({ botAuth }) => {
        academicsPage = new AcademicsPage(botAuth.page);
        await allure.description("This test case is to verify the creation of a qualification");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform & navigate to Qualification Overview", async () => {
            await navigateWithBotAuth(botAuth.page);
            await academicsPage.navigateToAcademics();
        });

        await allure.step("Navigate to Qualifications tab & click on Add Qualification button", async () => {
            const qualificationsDetails = await academicsPage.createQualification();
            if (qualificationsDetails) {
                console.log("Create a qualification test has passed");
            } else {
                console.error("Create a qualification test has failed");
            }
        });
    });

    test('Course creation', async ({ helperBotAuth }) => {
        academicsPage = new AcademicsPage(helperBotAuth.page);
        await allure.description("This test case is to verify the creation of a course");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform & navigate to Qualification Overview", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
            await academicsPage.navigateToAcademics();
        });

        await allure.step("Navigate to Courses tab & click on Add Course button", async () => {
            const courseDetails = await academicsPage.createCourse();
            if (courseDetails) {
                console.log("Creating a course test has passed");
            } else {
                console.error("Creating a course test has failed");
            }
        });
    });

    test('Subject Creation', async ({ botAuth }) => {
        academicsPage = new AcademicsPage(botAuth.page);
        await allure.description("This test case is to verify the creation of a subject");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform & navigate to Qualification Overview", async () => {
            await navigateWithBotAuth(botAuth.page);
            await academicsPage.navigateToAcademics();
        });

        await allure.step("Create a subject", async () => {
            const subjectDetails = await academicsPage.createSubject();
            if (subjectDetails) {
                console.log("Creating a subject test has passed");
            } else {
                console.error("Creating a subject test has failed");
            }
        });
    });

    test('Brief Creation', async ({ helperBotAuth }) => {
        academicsPage = new AcademicsPage(helperBotAuth.page);
        await allure.description("This test case is to verify the creation of a brief");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform & navigate to Qualification Overview", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
            await academicsPage.navigateToAcademics();
        });

        await allure.step("Create a brief", async () => {
            const documentDetails = await academicsPage.createBrief();
            if (documentDetails) {
                console.log("Creating a brief test has passed");
            } else {
                console.error("Creating a brief test has failed");
            }
        });
    });

    test.skip('Offsite Product', async ({ botAuth }) => {
        academicsPage = new AcademicsPage(botAuth.page);
        await allure.description("This test case is to verify that offsite products are accessible");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform & navigate to Qualification Overview", async () => {
            await navigateWithBotAuth(botAuth.page);
            await academicsPage.navigateToAcademics();
        });

        await allure.step("Access offsite product", async () => {
            const overviewHeader = await academicsPage.accessOffsiteProduct();
            if (overviewHeader) {
                console.log("Offsite product test has passed");
            } else {
                console.error("Offsite product test has failed");
            }
        });
    });

    test('Rise360 Concepts', async ({ helperBotAuth }) => {
        academicsPage = new AcademicsPage(helperBotAuth.page);
        await allure.description("This test case is to verify that Rise360 concepts are accessible");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform & navigate to Qualification Overview", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
            await academicsPage.navigateToAcademics();
        });

        await allure.step("Access Rise360 concepts", async () => {
            const conceptUploaded = await academicsPage.accessRise360Concepts();
            if (conceptUploaded) {
                console.log("Rise360 concept has been uploaded");
            } else {
                console.error("There is no Rise360 concept uploaded");
            }
        });

        await allure.step("Preview Rise360 course", async () => {
            const coursePreviewSuccessful = await academicsPage.previewRise360Course();
            if (coursePreviewSuccessful) {
                console.log("Rise360 concepts test has passed");
            } else {
                console.error("Rise360 concepts test has failed");
            }
        });
    });

    test.skip('Validate that the user can enter valid course commencement and end dates, and the format is correct', async ({ botAuth }) => {
        academicsPage = new AcademicsPage(botAuth.page);
        await allure.description("This test case is to verify the correct entry and format of course dates");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform & navigate to Course Detail", async () => {
            await navigateWithBotAuth(botAuth.page);
            await academicsPage.navigateToAcademics();
            await academicsPage.randomCourse();
        });

        await allure.step("Enter valid course commencement and end dates", async () => {
            await academicsPage.fillCommencementDate('17-Feb-2025');
            await academicsPage.fillEndDate('12-Dec-2025');

            const commencementDateValue = await academicsPage.getCommencementDateValue();
            const endDateValue = await academicsPage.getEndDateValue();

            expect(commencementDateValue).toBe('01-Feb-2024');
            expect(endDateValue).toBe('15-Dec-2024');

            const saveButton = botAuth.page.locator('button#c23');
            await saveButton.click();
            await botAuth.page.waitForTimeout(4000);
            const heading = await botAuth.page.locator('h3.page-header');
            await expect(heading).toBeVisible();
            await expect(heading).toHaveText('Qualification Management');
        });
    });

    test.skip('Enter a date in the course end date field that is in the past and verify that the system prevents submission', async ({ helperBotAuth }) => {
        academicsPage = new AcademicsPage(helperBotAuth.page);
        await allure.description("This test case is to verify that the system prevents submission of past dates");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform & navigate to Course Detail", async () => {
            await navigateWithBotAuth(helperBotAuth.page);
            await academicsPage.navigateToAcademics();
            await academicsPage.randomCourse();
        });

        await allure.step("Enter a past date and attempt to submit", async () => {
            await academicsPage.fillEndDate('01-Feb-2023');
            const saveButton = helperBotAuth.page.locator('button#c23');
            await saveButton.click();
            await helperBotAuth.page.waitForTimeout(4000);
            const warningContent = await helperBotAuth.page.locator('.swal-content p').innerText();
            expect(warningContent).toBe('The course commencment date cannot be later that the end date.');
        });
    });

    test.afterEach(async ({ botAuth, helperBotAuth }) => {
        const page = botAuth?.page || helperBotAuth?.page;
        await page.waitForTimeout(1000);
        await allure.attachment("Test Screenshot.png", await page.screenshot(), "image/png");
    });
});
