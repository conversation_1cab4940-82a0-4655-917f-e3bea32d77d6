import { expect, Page } from "@playwright/test";
import dotenv from "dotenv";
dotenv.config();

/**
 * Navigates to the application with invalid credentials.
 * @param {Page} page - The Playwright Page object.
 * @returns {Promise<void>}
 */
export async function invalidLoginProcess(page: Page): Promise<void> {
    const loginUrl = String(process.env.URL);
    console.log('Navigating to login URL:', loginUrl);
    await page.goto(loginUrl);
    const inputField = await page.getByPlaceholder("Username");
    if (inputField) {
        await inputField.fill("<EMAIL>");
    }
    const passwordInput = await page.getByPlaceholder("Password");
    if (passwordInput) {
        await passwordInput.fill(String(process.env.PASSWORD));
    }
    const loginButton = await page.getByRole("button", { name: "Login" });
    await loginButton?.click();
    const errorModal = await page.waitForSelector('.swal-modal');
    expect(await errorModal.isVisible()).toBeTruthy();
    expect(await page.locator('.swal-title').innerText()).toBe('Oh No!');
    expect(await page.locator('.swal-content p').innerText()).toBe('The information provided is not correct');
    await page.waitForTimeout(2000);
}

/**
 * Navigates to the application with bot user authentication.
 * @param {Page} page - The Playwright Page object.
 * @returns {Promise<void>}
 */
export async function navigateWithBotAuth(page: Page): Promise<void> {
    try {
        // Navigate directly to the authenticated URL
        const authUrl = String(process.env.AUTHURL);
        console.log('Navigating to authenticated URL:', authUrl);
        
        // Wait for navigation and any redirects to complete
        const response = await page.goto(authUrl, { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        console.log('Response status:', response?.status());
        console.log('Response URL:', response?.url());
        
        // Wait for the page to be stable
        await page.waitForLoadState('domcontentloaded');
        
        const currentUrl = page.url();
        console.log('Current URL after navigation:', currentUrl);
        
        if (currentUrl.includes('/UserManagement/login')) {
            console.error('Error: Redirected to login page. Authentication state may not be working.');
            console.log('Current cookies:', await page.context().cookies());
            throw new Error('Authentication failed - redirected to login page');
        }
    } catch (error) {
        console.error('Navigation error:', error);
        throw error;
    }
}

/**
 * Navigates to the application with helper bot authentication.
 * @param {Page} page - The Playwright Page object.
 * @returns {Promise<void>}
 */
export async function navigateWithHelperBotAuth(page: Page): Promise<void> {
    try {
        // Navigate directly to the authenticated URL
        const authUrl = String(process.env.AUTHURL);
        console.log('Navigating to authenticated URL:', authUrl);
        
        // Wait for navigation and any redirects to complete
        const response = await page.goto(authUrl, { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        console.log('Response status:', response?.status());
        console.log('Response URL:', response?.url());
        
        // Wait for the page to be stable
        await page.waitForLoadState('domcontentloaded');
        
        const currentUrl = page.url();
        console.log('Current URL after navigation:', currentUrl);
        
        if (currentUrl.includes('/UserManagement/login')) {
            console.error('Error: Redirected to login page. Authentication state may not be working.');
            console.log('Current cookies:', await page.context().cookies());
            throw new Error('Authentication failed - redirected to login page');
        }
    } catch (error) {
        console.error('Navigation error:', error);
        throw error;
    }
}

/**
 * Navigates to the exchange application with bot authentication.
 * @param {Page} page - The Playwright Page object.
 * @returns {Promise<void>}
 */
export async function navigateToExchangeWithAuth(page: Page): Promise<void> {
    try {
        const exchangeUrl = String(process.env.EXCHANGEURL);
        console.log('Navigating to exchange URL:', exchangeUrl);
        
        const response = await page.goto(exchangeUrl, { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        console.log('Response status:', response?.status());
        console.log('Response URL:', response?.url());
        
        await page.waitForLoadState('domcontentloaded');
        
        const currentUrl = page.url();
        console.log('Current URL after navigation:', currentUrl);
        
        if (currentUrl.includes('/UserManagement/login')) {
            console.error('Error: Redirected to login page. Authentication state may not be working.');
            console.log('Current cookies:', await page.context().cookies());
            throw new Error('Authentication failed - redirected to login page');
        }
    } catch (error) {
        console.error('Navigation error:', error);
        throw error;
    }
}