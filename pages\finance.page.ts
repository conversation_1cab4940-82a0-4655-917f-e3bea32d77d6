import { Page, expect, Locator } from '@playwright/test';
import { financeLocators, financeRoleLocators, monthMapping } from './finance.locators';

export class FinancePage {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    // Locator getters
    get financesMenu(): Locator {
        return this.page.locator(financeLocators.navigation.financesMenu);
    }

    get financialOverviewLink(): Locator {
        return this.page.locator(financeLocators.navigation.financialOverviewLink);
    }

    get debtorsAccountsLink(): Locator {
        return this.page.locator(financeLocators.navigation.debtorsAccountsLink);
    }

    get bankImportLink(): Locator {
        return this.page.locator(financeLocators.navigation.bankImportLink);
    }

    get debitOrdersLink(): Locator {
        return this.page.locator(financeLocators.navigation.debitOrdersLink);
    }

    get ageAnalysisLink(): Locator {
        return this.page.locator(financeLocators.navigation.ageAnalysisLink);
    }

    get fromDateInput(): Locator {
        return this.page.locator(financeLocators.dateRange.fromDateInput);
    }

    get toDateInput(): Locator {
        return this.page.locator(financeLocators.dateRange.toDateInput);
    }

    get applyButton(): Locator {
        return this.page.locator(financeLocators.dateRange.applyButton);
    }

    get startDateInput(): Locator {
        return this.page.getByLabel(financeRoleLocators.startDateLabel.label);
    }

    get endDateInput(): Locator {
        return this.page.getByLabel(financeRoleLocators.endDateLabel.label);
    }

    get dataGrid(): Locator {
        return this.page.locator(financeLocators.dataGrid.mainDataGrid);
    }

    get exportButton(): Locator {
        return this.page.locator(financeLocators.export.exportButton);
    }

    get progressModal(): Locator {
        return this.page.locator(financeLocators.export.progressModal);
    }

    get futureDebtorSlider(): Locator {
        return this.page.locator(financeLocators.debtors.futureDebtorSlider).first();
    }

    get applyFiltersButton(): Locator {
        return this.page.getByRole(financeRoleLocators.applyFiltersButton.role as 'button', {
            name: financeRoleLocators.applyFiltersButton.name
        });
    }

    get addDebtorsAccountButton(): Locator {
        return this.page.getByRole(financeRoleLocators.addDebtorsAccountButton.role as 'button', {
            name: financeRoleLocators.addDebtorsAccountButton.name
        });
    }

    get saveButton(): Locator {
        return this.page.locator(financeLocators.debtors.saveButton);
    }

    get debtorsAccountHeading(): Locator {
        return this.page.getByRole(financeRoleLocators.debtorsAccountHeading.role as 'heading', {
            name: financeRoleLocators.debtorsAccountHeading.name
        });
    }

    get importButton(): Locator {
        return this.page.locator(financeLocators.bankImport.importButton);
    }

    get confirmButton(): Locator {
        return this.page.getByRole(financeRoleLocators.confirmButton.role as 'button', {
            name: financeRoleLocators.confirmButton.name
        });
    }

    get csvImportModal(): Locator {
        return this.page.locator(financeLocators.bankImport.csvImportModal);
    }

    get addSubmissionButton(): Locator {
        return this.page.locator(financeLocators.debitOrders.addSubmissionButton);
    }

    get selectCurrencyText(): Locator {
        return this.page.locator(financeLocators.debitOrders.currencyModal);
    }

    get campusDropdown(): Locator {
        return this.page.locator(financeLocators.debitOrders.campusDropdown);
    }

    get campusCheckboxes(): Locator {
    return this.page.getByRole(financeRoleLocators.campusCheckbox.role as 'checkbox');
    }

    get generateReportButton(): Locator {
        return this.page.getByRole(financeRoleLocators.generateReportButton.role as 'button', {
            name: financeRoleLocators.generateReportButton.name
        });
    }

    get progressBar(): Locator {
        return this.page.locator(financeLocators.ageAnalysis.progressBar);
    }

    get dataTable(): Locator {
    return this.page.getByRole(financeRoleLocators.dataTable.role as 'table').locator('tbody');
    }

    get swalContent(): Locator {
        return this.page.locator(financeLocators.messages.swalContent);
    }

    get errorDialog(): Locator {
    return this.page.getByRole(financeRoleLocators.errorDialog.role as 'dialog');
    }

    // Action methods
    async navigateToFinancialOverview(): Promise<void> {
        await this.financesMenu.click();
        await this.financialOverviewLink.click();
        await this.page.waitForTimeout(3000);
    }

    async navigateToDebtorsAccount(): Promise<void> {
        await this.financesMenu.click();
        await this.debtorsAccountsLink.click();
        await this.page.waitForTimeout(3000);
    }

    async navigateToBankImport(): Promise<void> {
        await this.financesMenu.click();
        await this.bankImportLink.click();
        await this.page.waitForTimeout(3000);
    }

    async navigateToDebitOrders(): Promise<void> {
        await this.financesMenu.click();
        await this.debitOrdersLink.click();
        await this.page.waitForTimeout(3000);
    }

    async navigateToAgeAnalysis(): Promise<void> {
        await this.financesMenu.click();
        await this.ageAnalysisLink.click();
        await this.page.waitForTimeout(3000);
    }

    async selectDateRange(fromDate: string, toDate: string): Promise<void> {
        await this.fromDateInput.fill(fromDate);
        await this.page.waitForTimeout(2000);
        await this.toDateInput.fill(toDate);
        await this.applyButton.click();
    }

    async isDataGridVisible(): Promise<boolean> {
        await this.dataGrid.waitFor({ state: 'visible' });
        return await this.dataGrid.isVisible();
    }

    async clickExportButton(): Promise<void> {
        await this.exportButton.click();
    }

    async invalidExport(): Promise<void> {
        await this.exportButton.click();
        await expect(this.swalContent).toHaveText(financeLocators.messages.dateRangeErrorMessage);
        if (await this.swalContent.isVisible()) {
            console.log("Error message displayed for attempting to export without a date range.");
        } else {
            console.log("Error message is not visible");
        }
    }

    async confirmExport(): Promise<void> {
        await expect(this.progressModal).toBeVisible();
    }

    async selectFutureDebtor(): Promise<void> {
        await this.futureDebtorSlider.click();
        await this.page.waitForTimeout(2000);
    }

    async applyDebtorFilter(): Promise<void> {
        await this.applyFiltersButton.click();
    }

    async addDebtorsAccount(): Promise<void> {
        await this.addDebtorsAccountButton.click();
    }

    async saveDebtorsAccountModalWithoutFind(): Promise<void> {
        await this.saveButton.click();
        const contentMessage = await this.swalContent.innerText();
        await expect(contentMessage).toBe(financeLocators.messages.linkIndividualErrorMessage);
        if (await this.swalContent.isVisible()) {
            console.log("Warning message is visible");
        } else {
            console.log("Warning message is not visible");
        }
    }

    async isDebtorsAccountModalVisible(): Promise<boolean> {
        await expect(this.debtorsAccountHeading).toBeVisible();
        return true;
    }

    async clickImportButton(): Promise<void> {
        await this.importButton.waitFor({ state: 'visible' });
        await this.importButton.click();
    }

    async confirmImport(): Promise<void> {
        await this.confirmButton.click();
    }

    async isCsvImportModalVisible(): Promise<boolean> {
        await this.csvImportModal.waitFor({ state: 'visible', timeout: 10000 });
        return await this.csvImportModal.isVisible();
    }

    async addDebitOrderSubmission(): Promise<void> {
        await this.addSubmissionButton.click();
    }

    async isSelectCurrencyModalVisible(): Promise<boolean> {
        await this.selectCurrencyText.waitFor({ state: 'visible' });
        return await this.selectCurrencyText.isVisible();
    }

    async selectRandomCampus(): Promise<string> {
        try {
            // Wait for and click the dropdown
            await this.campusDropdown.waitFor({ state: 'visible', timeout: 5000 });
            await this.campusDropdown.click();

            // Wait for checkboxes to be visible and get them
            await this.campusCheckboxes.first().waitFor({ state: 'visible', timeout: 5000 });
            const allCampuses = await this.campusCheckboxes.all();

            if (allCampuses.length > 1) { // Skip the "All" checkbox at index 0
                const randomIndex = Math.floor(Math.random() * (allCampuses.length - 1)) + 1;
                const randomCampus = allCampuses[randomIndex];

                // Wait for and get the label text before clicking
                const label = randomCampus.locator('xpath=../label');
                await label.waitFor({ state: 'visible', timeout: 5000 });
                const selectedCampusText = await label.textContent() || '';

                await randomCampus.click();
                return selectedCampusText;
            } else {
                throw new Error("No campus options found after dropdown opened");
            }
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error(`Failed to select campus: ${error.message}`);
            } else {
                console.error('Failed to select campus:', error);
            }
            throw error; // Re-throw to make test fail explicitly
        }
    }

    private formatDateForInput(dateStr: string): string {
        // Handle date format "DD MMM YYYY"
        const [day, month, year] = dateStr.split(' ');
        const monthNum = monthMapping[month];

        // Pad day with leading zero if needed
        const paddedDay = day.padStart(2, '0');

        return `${year}-${monthNum}-${paddedDay}`;
    }

    async generateAgeAnalysisReport(fromDate: string, toDate: string): Promise<void> {
        // Format dates to YYYY-MM-DD for date inputs
        const formattedFromDate = this.formatDateForInput(fromDate);
        const formattedToDate = this.formatDateForInput(toDate);

        // Fill the start date input
        await this.startDateInput.fill(formattedFromDate);

        // Fill the end date input
        await this.endDateInput.fill(formattedToDate);

        // Click the generate report button
        await this.generateReportButton.click();

        // Wait for report generation to complete
        await this.waitForReportGeneration();
    }

    async generateAgeAnalysisReportWithoutSelectingCampus(): Promise<void> {
        // Click generate report button
        await this.generateReportButton.click();

        // Wait for and verify the error modal
        await expect(this.errorDialog).toBeVisible();

        // Verify modal title
        const modalTitle = this.errorDialog.getByText(financeLocators.messages.errorTitle);
        await expect(modalTitle).toBeVisible();

        // Verify error message
        const errorMessage = this.errorDialog.getByText(financeLocators.messages.campusErrorMessage);
        await expect(errorMessage).toBeVisible();

        // Click OK button to dismiss the modal
        await this.errorDialog.getByRole(financeRoleLocators.okButton.role as 'button', {
            name: financeRoleLocators.okButton.name
        }).click();

        console.log("Error modal verified successfully");
    }

    async waitForReportGeneration(): Promise<boolean> {
        try {
            // Wait for progress bar to be visible and reach 100%
            // Then wait for aria-valuenow to be 100
            await expect(this.progressBar).toHaveAttribute('aria-valuenow', '0', {
                timeout: 60000
            });

            // Finally wait for data table
            await expect(this.dataTable).toBeVisible({
                timeout: 15000
            });

            return true;
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Failed to wait for report generation:', error.message);
            } else {
                console.error('Failed to wait for report generation:', error);
            }
            throw error; // Let the test fail with the actual error
        }
    }
}
