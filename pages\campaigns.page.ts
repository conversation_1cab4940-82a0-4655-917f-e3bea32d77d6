import { Page, Locator } from '@playwright/test';
import { campaignLocators, campaignRoleLocators } from "./campaign.locators";

export class CampaignsPage {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    // Locator getters
    get crmMenu(): Locator {
        return this.page.locator(campaignLocators.navigation.crmMenu);
    }

    get campaignsMenu(): Locator {
        return this.page.locator(campaignLocators.navigation.campaignsMenu);
    }

    get campaignTiles(): Locator {
        return this.page.locator(campaignLocators.campaignTiles);
    }

    get campaignDataGrid(): Locator {
        return this.page.locator(campaignLocators.campaignDataGrid);
    }

    get newLeadButton(): Locator {
        return this.page.locator(campaignLocators.buttons.newLead);
    }

    get newCampaignButton(): Locator {
        return this.page.locator(campaignLocators.buttons.newCampaign);
    }

    get captureSessionsLink(): Locator {
        return this.page.getByRole(campaignRoleLocators.captureSessions.role as 'link', {
            name: campaignRoleLocators.captureSessions.name
        });
    }

    get newSessionButton(): Locator {
        return this.page.getByRole(campaignRoleLocators.newSession.role as 'button', {
            name: campaignRoleLocators.newSession.name
        });
    }

    get importLeadsButton(): Locator {
        return this.page.locator(campaignLocators.buttons.importLeads);
    }

    get leadDetailsModal(): Locator {
        return this.page.locator(campaignLocators.modals.leadDetails);
    }

    get campaignDetailsModal(): Locator {
        return this.page.locator(campaignLocators.modals.campaignDetails);
    }

    get newSessionModal(): Locator {
        return this.page.locator(campaignLocators.modals.newSession);
    }

    get fileUploadModal(): Locator {
        return this.page.locator(campaignLocators.modals.fileUpload);
    }

    get leadGeneratorOption(): Locator {
        return this.page.locator(campaignLocators.leadImport.leadGenerator);
    }

    get leadManagerOption(): Locator {
        return this.page.locator(campaignLocators.leadImport.leadManager);
    }

    // Action methods
    async navigateToCampaigns(): Promise<void> {
        await this.crmMenu.click();
        await this.campaignsMenu.click();
        await this.page.waitForTimeout(5000);
    }

    async selectRandomCampaignTile(): Promise<void> {
        const tiles = await this.campaignTiles.all();
        const randomTile = tiles[Math.floor(Math.random() * tiles.length)];
        const tileText = await randomTile.innerText();
        console.log(`Selected campaign category: ${tileText}`);
        await randomTile.click();
        await this.page.waitForTimeout(3000);
    }

    async selectRandomCampaignRow(): Promise<void> {
        const rows = await this.campaignDataGrid.all();
        const randomRow = Math.floor(Math.random() * rows.length);
        const rowText = await rows[randomRow].innerText();
        console.log(`Selected campaign row: ${rowText}`);
        await rows[randomRow].click();
        await this.page.waitForTimeout(5000);
    }

    async selectRandomCampaign(): Promise<void> {
        await this.selectRandomCampaignTile();
        await this.selectRandomCampaignRow();
    }

    async clickNewLeadButton(): Promise<void> {
        await this.newLeadButton.click();
        await this.page.waitForTimeout(4000);
    }

    async isNewLeadModalVisible(): Promise<boolean> {
        await this.page.waitForTimeout(2000);
        return await this.leadDetailsModal.isVisible();
    }

    async clickNewCampaignButton(): Promise<void> {
        await this.newCampaignButton.click();
        await this.page.waitForTimeout(2000);
    }

    async isNewCampaignModalVisible(): Promise<boolean> {
        return await this.campaignDetailsModal.isVisible();
    }

    async clickCaptureSessions(): Promise<void> {
        await this.captureSessionsLink.click();
        await this.page.waitForTimeout(1000);
    }

    async clickNewSession(): Promise<void> {
        await this.newSessionButton.click();
        await this.page.waitForTimeout(1000);
    }

    async isNewSessionModalVisible(): Promise<boolean> {
        return await this.newSessionModal.isVisible();
    }

    async clickImportLeadsButton(): Promise<void> {
        await this.importLeadsButton.click();
        await this.page.waitForTimeout(1000);
    }

    async selectLeadGenerator(): Promise<void> {
        await this.leadGeneratorOption.click();
        await this.page.waitForTimeout(2000);
    }

    async selectLeadManager(): Promise<void> {
        await this.leadManagerOption.click();
        await this.page.waitForTimeout(2000);
    }

    async isLeadUploadModalVisible(): Promise<boolean> {
        return await this.fileUploadModal.isVisible();
    }
}
