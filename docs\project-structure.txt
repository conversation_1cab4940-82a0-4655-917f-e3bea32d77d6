﻿# Project Structure
# Generated on: 2024-12-06 08:30:53
# Excluding patterns from .gitignore
#

# Ignoring: node_modules
# Ignoring: package-lock.json
# Ignoring: __pycache__
# Ignoring: *.py[cod]
# Ignoring: env
# Ignoring: .pytest_cache
# Ignoring: allure-results
# Ignoring: allure-history
# Ignoring: allure-report
# Ignoring: test-results
# Ignoring: playwright-report
# Ignoring: .idea
# Ignoring: .vscode
# Ignoring: .pytest_cache
# Ignoring: *.log
# Ignoring: logs
# Ignoring: .env
# Ignoring: .env.*

📁 .github
  📁 workflows
    📄 playwrightandallure.yml
    📄 playwright-prod.yml
📄 .gitignore
📁 data
  📄 generated-students.json
📁 docs
  📄 INCONNECT Test Automation Documentation v1.1.md
  📄 maintenance-procedures.md
  📄 setup-configuration-guide.md
  📄 test-case-documentation.md
📄 global-teardown.ts
📁 lib
  📄 logger.ts
  📄 storage-utils.js
  📄 storage-utils.ts
  📄 test-logger.ts
📄 LICENSE
📄 login-success.png
📄 navigation-failure.png
📄 package.json
📁 pages
  📄 academics.page.ts
  📄 analytics.page.ts
  📄 campaigns.page.ts
  📄 classroom-management.page.ts
  📄 communication.page.ts
  📄 compliance.page.ts
  📄 enrolment-management.page.ts
  📄 finance.page.ts
  📄 leadmanagement.page.ts
  📄 policies-procedures.page.ts
  📄 systemconfig.page.ts
  📄 systemconfig.ts
📄 playwright.config.ts
📄 prepare-testrail.js
📄 README.md
📄 test-failure.png
📁 test-objects
  📄 academics_objects.ts
  📄 analytics_objects.ts
  📄 campaign_objects.ts
  📄 classroom_management.ts
  📄 communication_objects.ts
  📄 compliance_objects.ts
  📄 enrolment_management_objects.ts
  📄 finance_module_objects.ts
  📄 login_process.ts
  📄 name_faker.js
  📄 policies_procedures_objects.ts
  📄 student_faker.cjs
  📄 student_faker.ts
📄 testrail.config.js
📁 testrail-results
  📄 junit-results.xml
📁 tests
  📄 table_test.spec.ts
  📄 test_academics.spec.ts
  📄 test_analytics_prod.spec.ts
  📄 test_campaigns_prod.spec.ts
  📄 test_classroom_management.spec.ts
  📄 test_communication.spec.ts
  📄 test_compliance.spec.ts
  📄 test_documents.spec.ts
  📄 test_enrolment_management.spec.ts
  📄 test_exchange.spec.ts
  📄 test_finance_prod.spec.ts
  📄 test_lead_management.spec.ts
  📄 test_policies_procedures.spec.ts
  📄 test_system_config.spec.ts
📄 tsconfig.json
📁 utils
