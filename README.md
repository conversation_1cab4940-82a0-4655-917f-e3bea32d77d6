# 🚀 INCONNECT: Test Management

![INCONNECT Logo](https://operations.inscape.co.za/assets/images/InConnectLogo_White_V3.svg)

## 🌟 Welcome to the Future of Learning!

INCONNECT is not just another LMS. It's a comprehensive, cutting-edge platform designed to look at a different way we approach education and training. From robust CRM capabilities to advanced analytics, INCONNECT has got you covered!

### 🧠 Why INCONNECT?

- **All-in-One Solution**: CRM, Academics, Finance, Student & Dashboards and Analytics - all under one roof!
- **Data-Driven Decisions**: Powerful analytics to drive your educational strategies
- **Financial Mastery**: Comprehensive finance module

## 🛠️ Test Management built for Regression

We use robust testing tools to ensure INCONNECT runs flawlessly:

- **Playwright**: For robust, cross-browser testing
- **Node.js**: Powering the test management backend

## 🔍 Features Under the Microscope

Our testing suite covers | More coverage areas will be added soon:

1. 🤝 **CRM Module**: Managing relationships like a pro
2. 📚 **Academics Module**: The heart of learning
3. 🔗 **External Integrations**: Playing well with others
4. 📊 **Analytics Module**: 
   - Create report categories
   - Generate insightful reports
   - Filter and analyze data with ease
5. 💰 **Finance Module**:
   - Financial overviews at your fingertips
   - Debtor account management
   - Bank imports and debit order submissions
   - Age Analysis reporting

## 🚀 Getting Started

1. Clone this repo
2. Install dependencies:
   ```
   npm install
   ```
3. Run the test suite:
   ```
   npx playwright test
   ```

## 📈 Continuous Improvement

Our automated testing suite ensures that every update, every new feature meets our high standards of quality and reliability.

---

Made with ❤️ by the INCONNECT Team
