import { Page, Locator } from '@playwright/test';
import { analyticsLocators, analyticsRoleLocators } from './analytics.locators';

export class AnalyticsPage {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    // Locator getters
    get systemConfigurationDropdown(): Locator {
        return this.page.locator(analyticsLocators.navigation.systemConfigurationDropdown);
    }

    get reportManagerLink(): Locator {
        return this.page.locator(analyticsLocators.navigation.reportManagerLink);
    }

    get dataAnalyticsLink(): Locator {
        return this.page.locator(analyticsLocators.navigation.dataAnalyticsLink)
            .filter({ hasText: 'Data Analytics' }).nth(1);
    }

    get reportDataGrid(): Locator {
        return this.page.locator(analyticsLocators.reports.dataGrid);
    }

    get reportDataGridRows(): Locator {
        return this.page.locator(analyticsLocators.reports.dataGridRows);
    }

    get reportSearchBar(): Locator {
        return this.page.locator(analyticsLocators.reports.searchBar);
    }

    get applyFilterButton(): Locator {
        return this.page.locator(analyticsLocators.reports.applyFilterButton);
    }

    get tableBody(): Locator {
        return this.page.locator(analyticsLocators.reports.tableBody);
    }

    get newCategoryButton(): Locator {
        return this.page.getByRole(analyticsRoleLocators.newCategoryButton.role as 'button', {
            name: analyticsRoleLocators.newCategoryButton.name
        });
    }

    get newReportButton(): Locator {
        return this.page.getByRole(analyticsRoleLocators.newReportButton.role as 'button', {
            name: analyticsRoleLocators.newReportButton.name,
            exact: analyticsRoleLocators.newReportButton.exact
        });
    }

    get reportCategoryNameInput(): Locator {
        return this.page.getByLabel(analyticsRoleLocators.reportCategoryNameInput.label);
    }

    get reportNameInput(): Locator {
        return this.page.getByLabel(analyticsRoleLocators.reportNameInput.label);
    }

    // Action methods
    async navigateToReportManager(): Promise<void> {
        await this.systemConfigurationDropdown.click();
        await this.reportManagerLink.click();
        await this.page.waitForTimeout(5000);
    }

    async navigateToDataAnalytics(): Promise<void> {
        await this.dataAnalyticsLink.click();
        await this.page.waitForTimeout(5000);
    }

    async clickNewReportCategory(): Promise<void> {
        await this.newCategoryButton.click();
        await this.page.waitForTimeout(5000);
    }

    async clickNewReport(): Promise<void> {
        await this.newReportButton.click();
    }

    async isReportCategoryInputVisible(): Promise<boolean> {
        return await this.reportCategoryNameInput.isVisible();
    }

    async isReportNameInputVisible(): Promise<boolean> {
        await this.page.waitForTimeout(5000);
        return await this.reportNameInput.isVisible();
    }

    async clickRandomReport(): Promise<void> {
        const dataGrid = await this.reportDataGridRows.all();
        const randomReport = Math.floor(Math.random() * dataGrid.length);
        console.log(`Random report: ${randomReport}`);
        await dataGrid[randomReport].click();
        await this.page.waitForTimeout(3000);
    }

    async isReportDataGridVisible(): Promise<boolean> {
        return await this.tableBody.isVisible();
    }

    async selectRandomReportFromGrid(): Promise<string> {
        const dataGridRows = await this.reportDataGrid.all();
        const numberOfRows = dataGridRows.length;
        const reports: string[] = [];

        for (let i = 1; i <= numberOfRows; i++) {
            const elementXPath = `//div[@id='ReportsDataGridObj-grid']//table/tbody/tr[${i}]/td[1]`;
            const option = await this.page.waitForSelector(elementXPath);
            const optionText = await option.textContent();
            if (optionText) {
                reports.push(optionText);
            }
        }
        const randomSearch = reports[Math.floor(Math.random() * reports.length)];
        await this.reportSearchBar.fill(randomSearch);
        console.log(`Random search: ${randomSearch}`);
        return randomSearch;
    }

    async searchRandomReport(): Promise<string> {
        const randomSearch = await this.selectRandomReportFromGrid();
        await this.applyFilterButton.click();
        await this.page.waitForTimeout(1000);
        return randomSearch;
    }

    async findRowWithRandomSearch(randomSearch: string): Promise<void> {
        const reportDataGrid = await this.reportDataGrid.all();
        for (let i = 0; i < reportDataGrid.length; i++) {
            const gridText = await reportDataGrid[i].textContent();
            if (gridText && gridText.includes(randomSearch)) {
                console.log(`Found the report: ${randomSearch}`);
                return;
            }
        }
        console.log(`Could not find the report: ${randomSearch}`);
    }

    async isReportDataGridRowVisible(): Promise<boolean> {
        const reportDataGrid = await this.reportDataGrid.first();
        return await reportDataGrid.isVisible();
    }
}
