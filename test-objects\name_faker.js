import { faker } from "@faker-js/faker/locale/en_ZA";

export const generateRandomName = () => {
    const randomFirstName = faker.person.firstName(); // Generate a random first name
    const randomLastName = faker.person.lastName(); // Generate a random last name
    return `${randomFirstName} ${randomLastName}`;
};

export const generateEmail = (fullName) => {
    const [firstName, lastName] = fullName.split(' ');
    return faker.internet.email({
      firstName, // Use the first name
      lastName, // Use the last name
      provider: "test.com", // Use a custom email provider
    });
  };

export const generateCellphone = () => {
    const prefixes = ["84", "83", "82", "79", "78", "76", "74", "73", "72"]; // South African cellphone number prefixes
    const index = Math.floor(Math.random() * prefixes.length); // Generate a random index
    const prefix = prefixes[index];
    const suffix = faker.number.int({min:1000000, max:9999999});
    return "+27" + prefix + suffix; // Generate a random South African cellphone number
};

// Log the generated name, email, and cellphone number
const randomName = generateRandomName();
console.log(randomName);
console.log(generateEmail(randomName));
console.log(generateCellphone());