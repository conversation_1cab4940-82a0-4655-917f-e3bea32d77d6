import * as fs from 'fs';
import * as path from 'path';

const STORAGE_DIR = path.join(__dirname, '..', 'data');
const STUDENTS_FILE = path.join(STORAGE_DIR, 'generated-students.json');

interface StorageUtils {
    saveStudent: (studentData: any) => void;
    getAllStudents: () => any[];
    initStorage: () => void;
}

export const storageUtils: StorageUtils = {
    initStorage: () => {
        if (!fs.existsSync(STORAGE_DIR)) {
            fs.mkdirSync(STORAGE_DIR, { recursive: true });
        }
        if (!fs.existsSync(STUDENTS_FILE)) {
            fs.writeFileSync(STUDENTS_FILE, JSON.stringify([], null, 2));
        }
    },

    saveStudent: (studentData) => {
        storageUtils.initStorage();
        const existingData = storageUtils.getAllStudents();
        existingData.push({
            ...studentData,
            generatedAt: new Date().toISOString()
        });
        fs.writeFileSync(STUDENTS_FILE, JSON.stringify(existingData, null, 2));
    },

    getAllStudents: () => {
        storageUtils.initStorage();
        const fileContent = fs.readFileSync(STUDENTS_FILE, 'utf-8');
        return JSON.parse(fileContent);
    }
};