import { faker } from "@faker-js/faker/locale/en_ZA";
import { storageUtils } from '../lib/storage-utils';

interface StudentInfo {
  firstName: string;
  lastName: string;
  cellphone: string;
  idNumber: string;
  email: string;
  password: string;
  parentEmail: string;
  parentMobile: string;
  physicalAddress: string;
  zipCode: string;
}

// Generate SA ID Number
const generateSAID = () => {
  // Generate date of birth (YYMMDD)
  const year = faker.number.int({ min: 1990, max: 2005 });
  const month = String(faker.number.int({ min: 1, max: 12 })).padStart(2, '0');
  const day = String(faker.number.int({ min: 1, max: 28 })).padStart(2, '0');
  const dob = `${String(year).slice(-2)}${month}${day}`;
  
  // Generate gender number (SSSS)
  // For females: 0000-4999, For males: 5000-9999
  const isMale = Math.random() < 0.5;
  const genderNumber = isMale 
    ? faker.number.int({ min: 5000, max: 9999 }) 
    : faker.number.int({ min: 0, max: 4999 });
  const genderStr = String(genderNumber).padStart(4, '0');
  
  // Citizenship (0 for SA citizen)
  const citizenship = '0';
  
  // Race indicator (8 for testing purposes)
  const race = '8';
  
  // Construct ID without checksum (12 digits)
  const idWithoutChecksum = `${dob}${genderStr}${citizenship}${race}`;
  
  // Calculate Luhn algorithm check digit
  const checksum = calculateLuhnChecksum(idWithoutChecksum);
  
  return `${idWithoutChecksum}${checksum}`;
};

const calculateLuhnChecksum = (idNumber: string): number => {
  let sum = 0;
  let alternate = false;
  
  // Loop through digits from right to left
  for (let i = idNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(idNumber[i]);
    
    if (alternate) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }
    
    sum += digit;
    alternate = !alternate;
  }
  
  return (10 - (sum % 10)) % 10;
};

// Generate SA cellphone number
const generateCellphone = () => {
  const prefixes = ["61", "62", "64", "71", "72", "74", "82", "83", "84"];
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const suffix = faker.number.int({min: 1000000, max: 9999999});
  return "+27" + prefix + suffix;
};

// Generate SA postal code
const generatePostalCode = () => {
  const majorCities = ["0001", "0002", "2000", "2001", "6000", "6001", "8000", "8001"];
  return majorCities[Math.floor(Math.random() * majorCities.length)];
};

// List of common South African suburbs
const suburbs = [
  "Sandton",
  "Rosebank",
  "Bryanston",
  "Norkem Park",
  "Kempton Park",
  "Fourways",
  "Randburg",
  "Centurion",
  "Morningside",
  "Midrand",
  "Waterkloof",
  "Brooklyn",
  "Hatfield",
  "Menlyn",
  "Sunninghill"
];

// Generate physical address with suburb
const generatePhysicalAddress = () => {
  const streetNumber = faker.number.int({ min: 1, max: 999 });
  const streetName = faker.location.street();
  const suburb = suburbs[Math.floor(Math.random() * suburbs.length)];
  const city = faker.location.city();
  
  return `${streetNumber} ${streetName}, ${suburb}, ${city}`;
};

export const generateStudentInfo = (): StudentInfo => {
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  
  const studentInfo = {
    firstName,
    lastName,
    cellphone: generateCellphone(),
    idNumber: generateSAID(),
    email: `${firstName.toLowerCase()}${lastName.toLowerCase()}@test.com`,
    password: faker.internet.password({ length: 12, prefix: 'Test@' }),
    parentEmail: faker.internet.email({ provider: "test.com" }),
    parentMobile: generateCellphone(),
    physicalAddress: generatePhysicalAddress(),
    zipCode: generatePostalCode()
  };

  // Save the generated student info
  storageUtils.saveStudent(studentInfo);
  
  return studentInfo;
};

// Generate and log student information
const studentInfo = generateStudentInfo();
console.log(JSON.stringify(studentInfo, null, 2));

// Log total number of generated students
const allStudents = storageUtils.getAllStudents();
console.log(`\nTotal generated students: ${allStudents.length}`);