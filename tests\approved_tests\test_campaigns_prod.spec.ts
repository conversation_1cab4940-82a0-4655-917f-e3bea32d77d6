import { test } from '../../test-objects/auth.fixture';
import { CampaignsPage } from '../../pages/campaigns.page';
import * as allure from "allure-js-commons";
import { Severity } from "allure-js-commons";
import { navigateWithBotAuth, navigateWithHelperBotAuth } from '../../test-objects/login_process';

test.describe('Campaigns Test Suite', () => {
    let campaignsPage: CampaignsPage;

    test('Add a new lead in a campaign', async ({ botAuth }) => {
        campaignsPage = new CampaignsPage(botAuth.page);
        await allure.description("New Lead Test");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await allure.step("Navigate to Campaigns", async () => {
            await campaignsPage.navigateToCampaigns();
        });

        await allure.step("Navigate to a random campaign", async () => {
            await campaignsPage.selectRandomCampaign();
        });

        await allure.step("Confirm New Lead Modal", async () => {
            await campaignsPage.clickNewLeadButton();
            const isVisible = await campaignsPage.isNewLeadModalVisible();
            console.log(isVisible ? "New Lead modal is displayed" : "New Lead modal is not displayed");
        });
    });

    test('Create a new Campaign', async ({ helperBotAuth }) => {
        campaignsPage = new CampaignsPage(helperBotAuth.page);
        await allure.description("New Campaign Test");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
        });

        await allure.step("Navigate to Campaigns", async () => {
            await campaignsPage.navigateToCampaigns();
        });

        await allure.step("Click on 'New Campaign' button", async () => {
            await campaignsPage.clickNewCampaignButton();
        });

        await allure.step("Confirm New Campaign Modal", async () => {
            const isVisible = await campaignsPage.isNewCampaignModalVisible();
            console.log(isVisible ? "Campaign modal is displayed" : "Campaign modal is not displayed");
        });
    });

    test('New Session', async ({ botAuth }) => {
        campaignsPage = new CampaignsPage(botAuth.page);
        await allure.description("New Session Test");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(botAuth.page);
        });

        await allure.step("Navigate to Campaigns", async () => {
            await campaignsPage.navigateToCampaigns();
        });

        await allure.step("Navigate to a random campaign", async () => {
            await campaignsPage.selectRandomCampaign();
        });

        await allure.step("Click on 'Capture Sessions' button & 'New Session' button", async () => {
            await campaignsPage.clickCaptureSessions();
            await campaignsPage.clickNewSession();
            const isVisible = await campaignsPage.isNewSessionModalVisible();
            console.log(isVisible ? "Session modal is displayed" : "Session modal is not displayed");
        });
    });

    test('Lead Upload', async ({ helperBotAuth }) => {
        campaignsPage = new CampaignsPage(helperBotAuth.page);
        await allure.description("Lead Upload Test");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
        });

        await allure.step("Navigate to Campaigns", async () => {
            await campaignsPage.navigateToCampaigns();
        });

        await allure.step("Navigate to a random campaign", async () => {
            await campaignsPage.selectRandomCampaign();
        });

        await allure.step("Lead Import process", async () => {
            await campaignsPage.clickImportLeadsButton();
            await campaignsPage.selectLeadGenerator();
            await campaignsPage.selectLeadManager();
            const isVisible = await campaignsPage.isLeadUploadModalVisible();
            console.log(isVisible ? "Lead upload modal is displayed" : "Lead upload modal is not displayed");
        });
    });

    test.afterEach(async ({ page }, testInfo) => {
        if (testInfo.status !== testInfo.expectedStatus) {
            // Take screenshot only on failure
            const screenshot = await page.screenshot();
            await testInfo.attach('screenshot', { body: screenshot, contentType: 'image/png' });
        }
    });
});