import { expect, Page, Locator } from '@playwright/test';
import { policiesProceduresLocators, policiesProceduresRoleLocators } from './policies-procedures.locators';
import * as fs from 'fs/promises';
import * as path from 'path';

export class PoliciesProceduresPage {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    // Locator getters
    get policiesAndProceduresLink(): Locator {
        return this.page.getByRole(policiesProceduresRoleLocators.policiesAndProceduresLink.role as 'link', {
            name: policiesProceduresRoleLocators.policiesAndProceduresLink.name
        });
    }

    get itemsPerPageButton(): Locator {
        return this.page.getByRole(policiesProceduresRoleLocators.itemsPerPageButton.role as 'button', {
            name: policiesProceduresRoleLocators.itemsPerPageButton.name
        });
    }

    get fiftyItemsOption(): Locator {
        return this.page.getByText(policiesProceduresRoleLocators.fiftyItemsText.text, {
            exact: policiesProceduresRoleLocators.fiftyItemsText.exact
        });
    }

    get policyRows(): Locator {
        return this.page.locator(policiesProceduresLocators.table.policyRows);
    }

    get swalModal(): Locator {
        return this.page.locator(policiesProceduresLocators.modal.swalModal);
    }

    get swalTitle(): Locator {
        return this.page.locator(policiesProceduresLocators.modal.swalTitle);
    }

    get confirmButton(): Locator {
        return this.page.getByRole(policiesProceduresRoleLocators.confirmButton.role as 'button', {
            name: policiesProceduresRoleLocators.confirmButton.name
        });
    }

    // Action methods
    async navigateToPoliciesProcedures(): Promise<void> {
        await this.policiesAndProceduresLink.click();
        await this.page.waitForTimeout(3000);
        // Increase items per page for better random selection
        await this.itemsPerPageButton.click();
        await this.fiftyItemsOption.click();
    }

    async getRandomPolicyButton(): Promise<{ button: Locator, name: string }> {
        await this.page.waitForTimeout(3000); // Wait for list to load
        const policyRows = await this.policyRows.all();

        if (policyRows.length === 0) {
            throw new Error('No policies found in the table');
        }

        const randomIndex = Math.floor(Math.random() * policyRows.length);
        const row = policyRows[randomIndex];

        // Get policy name from the row
        const nameCell = row.locator(policiesProceduresLocators.table.nameCell).first();
        const name = await nameCell.textContent() || 'Unknown Policy';

        // Get the download button from the row
        const button = row.locator(policiesProceduresLocators.table.downloadButton);
        if (!(await button.isVisible())) {
            throw new Error('No download button found in selected row');
        }

        return { button, name };
    }

    async handleConfirmationDialog(): Promise<void> {
        try {
            await this.swalModal.waitFor({ state: 'visible', timeout: 2000 });
            await this.swalTitle.waitFor({ state: 'visible' });
            await this.confirmButton.click();
            await this.page.waitForTimeout(1000);
        } catch (error: unknown) {
            if (error instanceof Error) {
                if (error.name !== 'TimeoutError') {
                    throw error;
                }
            } else {
                throw error;
            }
            // No modal appeared, which is fine
        }
    }

    /**
     * Downloads a random policy document and verifies it
     * @param outputDir Directory to save the downloaded file
     * @returns Path to the downloaded file
     */
    async downloadRandomPolicy(outputDir: string): Promise<string> {
        // Start download process
        const downloadPromise = this.page.waitForEvent('download', { timeout: 30000 });

        // Get a random policy and click its download button
        const { button, name } = await this.getRandomPolicyButton();
        await button.click();

        // Handle any confirmation dialog
        await this.handleConfirmationDialog();

        // Wait for and handle the download
        const download = await downloadPromise;
        const suggestedName = download.suggestedFilename();
        const sanitizedName = name.replace(/[^a-z0-9]/gi, '_').toLowerCase();
        const filePath = path.join(outputDir, `${sanitizedName}-${suggestedName}`);

        await download.saveAs(filePath);
        console.log(`Downloaded policy "${name}" to: ${filePath}`);

        // Verify the download
        await this.verifyDownloadedFile(filePath);

        return filePath;
    }

    /**
     * Verifies that the downloaded file is valid
     * @param filePath Path to the downloaded file
     */
    private async verifyDownloadedFile(filePath: string): Promise<void> {
        const fileStats = await fs.stat(filePath);
        expect(fileStats.size, 'Downloaded file should not be empty').toBeGreaterThan(0);

        if (filePath.toLowerCase().endsWith('.pdf')) {
            const fileContent = await fs.readFile(filePath);
            const isPDF = fileContent.toString('ascii').startsWith('%PDF-');
            expect(isPDF, 'File should be a valid PDF').toBeTruthy();
        }
    }

    /**
     * Checks if a PDF viewer opens in a new tab
     * @returns true if PDF viewer is displayed, false otherwise
     */
    async checkPDFViewer(): Promise<boolean> {
        const newPagePromise = this.page.context().waitForEvent('page', { timeout: 5000 }).catch(() => null);

        try {
            const newPage = await newPagePromise;
            if (!newPage) {
                return false;
            }

            try {
                await newPage.waitForLoadState('networkidle', { timeout: 10000 });
                const fileUrl = newPage.url();

                const isPDF = fileUrl.includes('.pdf');
                if (isPDF) {
                    console.log('Document opened in PDF viewer');
                }
                return isPDF;
            } finally {
                if (!newPage.isClosed()) {
                    await newPage.close();
                }
            }
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Error checking PDF viewer:', error.message);
            } else {
                console.error('Error checking PDF viewer:', error);
            }
            return false;
        }
    }
}
