import { Page, Locator } from '@playwright/test';
import { academicsLocators, academicsRoleLocators } from './academics.locators';

export class AcademicsPage {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    // Locator getters
    get academicsNavbar(): Locator {
        return this.page.locator(academicsLocators.navigation.academicsNavbar);
    }

    get coursesLink(): Locator {
        return this.page.locator(academicsLocators.navigation.coursesLink);
    }

    get qualificationTypeSelect(): Locator {
        return this.page.locator(academicsLocators.filters.qualificationTypeSelect);
    }

    get academicYearSelect(): Locator {
        return this.page.locator(academicsLocators.filters.academicYearSelect);
    }

    get rise360AcademicYearSelect(): Locator {
        return this.page.locator(academicsLocators.filters.rise360AcademicYearSelect);
    }

    get applyFilterButton(): Locator {
        return this.page.locator(academicsLocators.filters.applyFilterButton);
    }

    get courseList(): Locator {
        return this.page.locator(academicsLocators.courses.courseList);
    }

    get subjectsTab(): Locator {
        return this.page.getByRole(academicsRoleLocators.subjectsTab.role as 'tab', {
            name: academicsRoleLocators.subjectsTab.name
        });
    }

    get subjectGrid(): Locator {
        return this.page.locator(academicsLocators.subjects.subjectGrid);
    }

    get addSubjectButton(): Locator {
        return this.page.getByRole(academicsRoleLocators.addSubjectButton.role as 'button', {
            name: academicsRoleLocators.addSubjectButton.name
        });
    }

    get qualificationsTabList(): Locator {
        return this.page.locator(academicsLocators.qualifications.tabList);
    }

    get addQualificationButton(): Locator {
        return this.page.locator(academicsLocators.qualifications.addButton);
    }

    get qualificationModal(): Locator {
        return this.page.locator(academicsLocators.qualifications.modal);
    }

    get addCourseButton(): Locator {
        return this.page.locator(academicsLocators.courseManagement.addCourseButton);
    }

    get courseModal(): Locator {
        return this.page.locator(academicsLocators.courseManagement.courseModal);
    }

    get addDocumentButton(): Locator {
        return this.page.getByRole(academicsRoleLocators.addDocumentButton.role as 'button', {
            name: academicsRoleLocators.addDocumentButton.name
        });
    }

    get documentModal(): Locator {
        return this.page.getByRole(academicsRoleLocators.documentDetailsHeading.role as 'heading', {
            name: academicsRoleLocators.documentDetailsHeading.name
        });
    }

    get courseModuleDetailsHeading(): Locator {
        return this.page.getByRole(academicsRoleLocators.courseModuleDetailsHeading.role as 'heading', {
            name: academicsRoleLocators.courseModuleDetailsHeading.name
        });
    }

    get offsiteDropdown(): Locator {
        return this.page.locator(academicsLocators.offsite.dropdown);
    }

    get offsiteGoToOption(): Locator {
        return this.page.locator(academicsLocators.offsite.goToOption);
    }

    get offsiteViewContentButton(): Locator {
        return this.page.locator(academicsLocators.offsite.viewContentButton);
    }

    get offsiteOverviewText(): Locator {
        return this.page.locator(academicsLocators.offsite.overviewText);
    }

    get coursewareTab(): Locator {
        return this.page.locator(academicsLocators.rise360.coursewareTab);
    }

    get rise360TableRow(): Locator {
        return this.page.locator(academicsLocators.rise360.tableRow).first();
    }

    get conceptInput(): Locator {
        return this.page.locator(academicsLocators.rise360.conceptInput);
    }

    get previewButton(): Locator {
        return this.page.locator(academicsLocators.rise360.previewButton);
    }

    get commencementDateInput(): Locator {
        return this.page.locator(academicsLocators.forms.commencementDate);
    }

    get endDateInput(): Locator {
        return this.page.locator(academicsLocators.forms.endDate);
    }

    get submitButton(): Locator {
        return this.page.locator(academicsLocators.forms.submitButton);
    }

    get errorMessage(): Locator {
        return this.page.locator(academicsLocators.forms.errorMessage);
    }

    // Action methods
    async navigateToAcademics(): Promise<void> {
        await this.academicsNavbar.click();
        await this.coursesLink.click();
        await this.page.waitForTimeout(3000);
    }

    async filterCourses(): Promise<void> {
        await this.page.waitForTimeout(3000);
        const randomValue = Math.floor(Math.random() * 3) + 2;
        const randomValueStr = randomValue.toString();

        // Verify options exist before selecting
        const options = await this.qualificationTypeSelect.locator('option').all();
        if (options.length === 0) {
            throw new Error('No options found in qualification type select element');
        }

        await this.qualificationTypeSelect.selectOption(randomValueStr);
        console.log(`Selected qualification type: ${randomValueStr}`);

        let academicValue: number;
        if (randomValue === 4) {
            academicValue = 1; // If qualification type is "Higher Cert", select "1st Year"
        } else {
            academicValue = Math.floor(Math.random() * 3) + 1; // Otherwise, select a random academic year
        }
        const academicValueStr = academicValue.toString();

        // Verify academic year options exist before selecting
        const academicOptions = await this.academicYearSelect.locator('option').all();
        if (academicOptions.length === 0) {
            throw new Error('No options found in academic year select element');
        }

        await this.academicYearSelect.selectOption(academicValueStr);
        console.log(`Selected academic year: ${academicValueStr}`);
        await this.applyFilterButton.click();
        await this.page.waitForTimeout(5000); // Wait for data to load
    }

    async filterCoursesOffsite(): Promise<void> {
        await this.page.waitForTimeout(3000);
        const randomValue = Math.floor(Math.random() * 2) + 2;
        const randomValueStr = randomValue.toString();

        await this.qualificationTypeSelect.selectOption(randomValueStr);
        console.log(`Selected qualification type: ${randomValueStr}`);

        let academicValue: number;
        if (randomValue === 4) {
            academicValue = 1; // If qualification type is "Higher Cert", select "1st Year"
        } else {
            academicValue = Math.floor(Math.random()) + 3; // Otherwise, select a random academic year
        }
        const academicValueStr = academicValue.toString();

        await this.academicYearSelect.selectOption(academicValueStr);
        console.log(`Selected academic year: ${academicValueStr}`);
        await this.applyFilterButton.click();
        await this.page.waitForTimeout(5000); // Wait for data to load
    }

    async filterCoursesRise360(): Promise<void> {
        await this.page.waitForTimeout(3000);
        const randomValue = Math.floor(Math.random()) + 2;
        const randomValueStr = randomValue.toString();

        await this.qualificationTypeSelect.selectOption(randomValueStr);
        console.log(`Selected qualification type: ${randomValueStr}`);

        let academicValue: number;
        if (randomValue === 4) {
            academicValue = 1; // If qualification type is "Higher Cert", select "1st Year"
        } else {
            academicValue = Math.floor(Math.random()) + 1; // Otherwise, select a random academic year
        }
        const academicValueStr = academicValue.toString();

        await this.rise360AcademicYearSelect.selectOption(academicValueStr);
        console.log(`Selected academic year: ${academicValueStr}`);
        await this.applyFilterButton.click();
        await this.page.waitForTimeout(5000); // Wait for data to load
    }

    async selectRandomCourse(): Promise<void> {
        const courses = await this.courseList.all();
        const randomCourseIndex = Math.floor(Math.random() * (courses.length - 1));
        console.log(`Random course index: ${randomCourseIndex}`);
        await courses[randomCourseIndex].click();
        await this.page.waitForTimeout(3000);
    }

    async selectRandomCourseAndNavigateToSubjectTab(): Promise<void> {
        const courses = await this.courseList.all();
        const randomCourseIndex = Math.floor(Math.random() * (courses.length - 1));
        console.log(`Random course index: ${randomCourseIndex}`);
        await courses[randomCourseIndex].click();
        await this.page.waitForTimeout(6000);
        await this.subjectsTab.click();
        await this.page.waitForTimeout(8000);
    }

    async selectRandomSubject(): Promise<void> {
        const subjects = await this.subjectGrid.all();
        if (subjects.length > 0) {
            const randomSubjectIndex = Math.floor(Math.random() * subjects.length);
            await subjects[randomSubjectIndex].click(); // Click on a random subject
            await this.page.waitForTimeout(3000); // Wait for subject details to load
        } else {
            console.error("No subjects found in the subject grid");
        }
    }

    async createQualification(): Promise<Locator> {
        await this.qualificationsTabList.click();
        await this.addQualificationButton.click();
        return this.qualificationModal;
    }

    async createCourse(): Promise<Locator> {
        await this.addCourseButton.waitFor({ state: 'visible' });
        await this.addCourseButton.click();
        return this.courseModal;
    }

    async createSubject(): Promise<Locator> {
        await this.filterCourses();
        await this.selectRandomCourseAndNavigateToSubjectTab();
        await this.addSubjectButton.click();
        return this.courseModuleDetailsHeading;
    }

    async createBrief(): Promise<Locator> {
        await this.filterCourses();
        await this.selectRandomCourseAndNavigateToSubjectTab();
        await this.selectRandomSubject();
        await this.addDocumentButton.click();
        await this.page.waitForTimeout(5000);
        return this.documentModal;
    }

    async accessOffsiteProduct(): Promise<Locator> {
        await this.filterCoursesOffsite();
        await this.selectRandomCourseAndNavigateToSubjectTab();
        await this.selectRandomSubject();
        await this.offsiteDropdown.click();
        await this.offsiteGoToOption.click();
        await this.page.waitForTimeout(3000);
        await this.offsiteViewContentButton.click();
        return this.offsiteOverviewText;
    }

    async accessRise360Concepts(): Promise<boolean> {
        await this.filterCoursesRise360();
        await this.selectRandomCourseAndNavigateToSubjectTab();
        await this.selectRandomSubject();
        await this.coursewareTab.click();

        await this.rise360TableRow.waitFor({ state: 'attached' });
        await this.rise360TableRow.click();
        await this.page.waitForTimeout(3000);

        await this.conceptInput.waitFor({ state: 'attached' });
        const isDisabled = await this.conceptInput.getAttribute('disabled');
        return isDisabled !== null;
    }

    async previewRise360Course(): Promise<boolean> {
        await this.previewButton.click();
        return new Promise<boolean>(resolve => {
            this.page.once('popup', async (popup) => {
                await popup.waitForLoadState('domcontentloaded');
                const startCourseButton = popup.getByRole('link', {
                    name: academicsRoleLocators.startCourseLink.name
                });
                await startCourseButton.click();
                await popup.waitForTimeout(3000);
                const lessonTitle = popup.locator(academicsLocators.rise360.lessonTitle)
                    .getByText('Subject Overview');
                resolve(lessonTitle !== null);
            });
        });
    }

    async randomCourse(): Promise<void> {
        await this.filterCourses();
        await this.selectRandomCourse();
        await this.page.waitForTimeout(3000);
    }

    async fillCommencementDate(date: string): Promise<void> {
        await this.commencementDateInput.fill(date);
    }

    async fillEndDate(date: string): Promise<void> {
        await this.endDateInput.fill(date);
    }

    async getCommencementDateValue(): Promise<string> {
        return await this.commencementDateInput.inputValue();
    }

    async getEndDateValue(): Promise<string> {
        return await this.endDateInput.inputValue();
    }

    async clickSubmitButton(): Promise<void> {
        await this.submitButton.click();
    }

    async getErrorMessage(): Promise<Locator> {
        return this.errorMessage;
    }
}
