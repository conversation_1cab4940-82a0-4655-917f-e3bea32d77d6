import { Page, Locator } from "@playwright/test";

export class SystemConfigPage {
    private readonly page: Page;
    private readonly systemConfigLink: Locator;
    private readonly workflow: Locator;
    private readonly newEnrolment: Locator;
    readonly enrolmentLink: Locator;
    readonly newEnrolmentStatus: Locator;
    private readonly distanceAccredited: Locator;
    private readonly distanceFCAdC: Locator;
    private readonly addWorkflow: Locator;
    readonly workflowHeading: Locator;
    private readonly enrolPaymentPlan: Locator;
    private readonly addTemplate: Locator;
    readonly templateHeading: Locator;
    private readonly devicePaymentPlan: Locator;
    readonly deviceTemplateHeading: Locator;

    constructor(page: Page) {
        this.page = page;
        this.systemConfigLink = page.getByText('System Configuration', { exact: true });
        this.workflow = page.getByRole('link', { name: 'Workflow', exact: true });
        this.newEnrolment = page.getByRole('cell', {name: 'NewEnrolment25'});
        this.enrolmentLink = page.getByLabel('Workflow Url');
        this.newEnrolmentStatus = page.locator('#c13');
        this.distanceAccredited = page.getByRole('cell', {name: 'Dist Accredited'});
        this.distanceFCAdC = page.getByRole('cell', {name: 'Dist_FC_&_AdC'});
        this.addWorkflow = page.getByRole('button', {name: 'Add Workflow'});
        this.workflowHeading = page.locator('text=Workflow Details');
        this.enrolPaymentPlan = page.getByRole('link', { name: 'Enrolment', exact: true }).nth(1);
        this.addTemplate = page.getByRole('button', {name: 'Add Template'});
        this.templateHeading = page.locator('text=Enrolment Payment Plan Template Details');
        this.devicePaymentPlan = page.getByRole('link', { name: 'Device', exact: true });
        this.deviceTemplateHeading = page.locator('text=Device Payment Plan Template Details');
    }

    async navigateToSystemConfig(): Promise<void> {
        await this.systemConfigLink.click();
    }

    async navigateToWorkflow(): Promise<void> {
        await this.workflow.click();
    }

    async clickNewEnrolment(): Promise<void> {
        await this.newEnrolment.click();
    }

    async clickDistanceAccredited(): Promise<void> {
        await this.distanceAccredited.click();
    }

    async clickDistanceFCAdC(): Promise<void> {
        await this.distanceFCAdC.click();
    }

    async clickAddWorkflow(): Promise<void> {
        await this.addWorkflow.click();
    }

    async navigateToEnrolPaymentPlan(): Promise<void> {
        await this.enrolPaymentPlan.click();
    }

    async clickAddTemplate(): Promise<void> {
        await this.addTemplate.click();
    }

    async navigateToDevicePaymentPlan(): Promise<void> {
        await this.devicePaymentPlan.click();
    }

    async navigateToWorkflowPage(): Promise<void> {
        await this.navigateToSystemConfig();
        await this.navigateToWorkflow();
    }

    async checkEnrolmentWorkflow(): Promise<void> {
        await this.clickNewEnrolment();
        await this.page.waitForTimeout(1000);
    }

    async checkDistanceAccreditedWorkflow(): Promise<void> {
        await this.clickDistanceAccredited();
        await this.page.waitForTimeout(1000);
    }

    async checkDistanceNonAccreditedWorkflow(): Promise<void> {
        await this.clickDistanceFCAdC();
        await this.page.waitForTimeout(1000);
    }

    async addNewWorkflow(): Promise<void> {
        await this.clickAddWorkflow();
        await this.page.waitForTimeout(1000);
    }

    async addEnrolmentPaymentPlanTemplate(): Promise<void> {
        await this.navigateToEnrolPaymentPlan();
        await this.clickAddTemplate();
        await this.page.waitForTimeout(1000);
    }

    async addDevicePaymentPlanTemplate(): Promise<void> {
        await this.navigateToDevicePaymentPlan();
        await this.clickAddTemplate();
        await this.page.waitForTimeout(1000);
    }
}