import { Page, Locator } from '@playwright/test';
import { complianceLocators, complianceRoleLocators, nlrdCategories, dhetReports } from './compliance.locators';

export class CompliancePage {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    // Locator getters
    get complianceLink(): Locator {
        return this.page.getByRole(complianceRoleLocators.complianceLink.role as 'link', {
            name: complianceRoleLocators.complianceLink.name
        });
    }

    get generateReportButton(): Locator {
        return this.page.getByRole(complianceRoleLocators.generateReportButton.role as 'button', {
            name: complianceRoleLocators.generateReportButton.name
        });
    }

    get campusRadioInputs(): Locator {
        return this.page.locator(complianceLocators.campus.radioInputs);
    }

    get combobox(): Locator {
    return this.page.getByRole(complianceRoleLocators.combobox.role as 'combobox');
    }

    // Helper method to get category cell by name
    getCategoryCell(categoryName: string): Locator {
    return this.page.getByRole(complianceRoleLocators.categoryCell.role as 'cell', { name: categoryName });
    }

    // Helper method to get campus label by ID
    getCampusLabel(campusId: string): Locator {
        return this.page.locator(`label[for="${campusId}"]`);
    }

    // Action methods
    async navigateToCompliance(): Promise<void> {
        await this.complianceLink.click();
    }

    async selectCategory(categoryName: string): Promise<void> {
        await this.getCategoryCell(categoryName).click();
    }

    async selectRandomCampus(): Promise<void> {
        const campusOptions = await this.campusRadioInputs.evaluateAll((inputs: HTMLInputElement[]) =>
            inputs.map(input => input.id)
        );
        const randomIndex = Math.floor(Math.random() * campusOptions.length);
        const campusId = campusOptions[randomIndex];
        await this.getCampusLabel(campusId).click();
    }

    async generateReport(): Promise<void> {
        await this.generateReportButton.click();
    }

    async selectAndDownloadNLRDDataSubmissionFiles(): Promise<Array<{ category: string, downloadPromise: Promise<any> }>> {
        const downloadPromises: Array<{ category: string, downloadPromise: Promise<any> }> = [];

        for (const category of nlrdCategories) {
            await this.selectCategory(category);
            await this.selectRandomCampus();
            // Start waiting for download before clicking generate
            const downloadPromise = this.page.waitForEvent('download');
            await this.generateReport();
            downloadPromises.push({ category, downloadPromise });
        }

        return downloadPromises;
    }

    async selectAndDownloadDHETReportFiles(): Promise<Array<{ name: string, downloadPromise: Promise<any> }>> {
        const downloadPromises: Array<{ name: string, downloadPromise: Promise<any> }> = [];

        for (const report of dhetReports) {
            await this.selectCategory(report.name);
            if (report.option) {
                await this.combobox.selectOption(report.option);
            }
            // Start waiting for download before clicking generate
            const downloadPromise = this.page.waitForEvent('download');
            await this.generateReport();
            downloadPromises.push({ name: report.name, downloadPromise });
        }

        return downloadPromises;
    }
}
