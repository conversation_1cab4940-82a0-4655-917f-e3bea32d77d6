import { expect } from '@playwright/test';
import { test } from '../../test-objects/auth.fixture';
import * as fs from 'fs/promises';
import * as allure from "allure-js-commons";
import { Severity } from "allure-js-commons";
import { navigateWithBotAuth } from '../../test-objects/login_process';
import { CompliancePage } from "../../pages/compliance.page";

test.describe('Compliance Overview', () => {
    let compliancePage: CompliancePage;

    test.beforeEach(async ({ page }) => {
        compliancePage = new CompliancePage(page);
    });

    test('Open or Download Compliance documents', async ({ page }, testInfo) => {
        await allure.description("Open or Download Compliance documents");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithBotAuth(page);
        });

        await allure.step("Navigate to Compliance", async () => {
            await compliancePage.navigateToCompliance();
        });

        await allure.step("Select and download NLRD Data Submission Files", async () => {
            const downloadPromises = await compliancePage.selectAndDownloadNLRDDataSubmissionFiles();
            
            // Handle each NLRD file download
            for (const { category, downloadPromise } of downloadPromises) {
                const download = await downloadPromise;
                const filePath = testInfo.outputPath(`nlrd-${category}.xlsx`);
                await download.saveAs(filePath);
                
                // Verify downloaded file
                const fileStats = await fs.stat(filePath);
                expect(fileStats.size, `NLRD file ${category} should not be empty`).toBeGreaterThan(0);
                expect(filePath.toLowerCase().endsWith('.xlsx'), 
                    `File ${category} should be an Excel file`).toBeTruthy();
            }
        });

        await allure.step("Select and download DHET Report Files", async () => {
            const downloadPromises = await compliancePage.selectAndDownloadDHETReportFiles();
            
            // Handle each DHET file download
            for (const { name, downloadPromise } of downloadPromises) {
                const download = await downloadPromise;
                const suggestedName = download.suggestedFilename();
                const filePath = testInfo.outputPath(`dhet-${name}-${suggestedName}`);
                await download.saveAs(filePath);
                
                // Verify downloaded file
                const fileStats = await fs.stat(filePath);
                expect(fileStats.size, `DHET file ${name} should not be empty`).toBeGreaterThan(0);
                
                // Check if file is Excel or PDF based on extension
                const ext = suggestedName.toLowerCase();
                expect(ext.endsWith('.xlsx') || ext.endsWith('.xls') || ext.endsWith('.pdf'),
                    `File ${name} should be an Excel or PDF file`).toBeTruthy();
            }
        });
    });

    test.afterEach(async ({ page }) => {
        await page.waitForTimeout(1000);
        await allure.attachment("Test Screenshot.png", await page.screenshot(), "image/png");
    });
});