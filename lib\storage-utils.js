"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.storageUtils = void 0;
var fs = require("fs");
var path = require("path");
var STORAGE_DIR = path.join(__dirname, '..', 'data');
var STUDENTS_FILE = path.join(STORAGE_DIR, 'generated-students.json');
exports.storageUtils = {
    initStorage: function () {
        if (!fs.existsSync(STORAGE_DIR)) {
            fs.mkdirSync(STORAGE_DIR, { recursive: true });
        }
        if (!fs.existsSync(STUDENTS_FILE)) {
            fs.writeFileSync(STUDENTS_FILE, JSON.stringify([], null, 2));
        }
    },
    saveStudent: function (studentData) {
        exports.storageUtils.initStorage();
        var existingData = exports.storageUtils.getAllStudents();
        existingData.push(__assign(__assign({}, studentData), { generatedAt: new Date().toISOString() }));
        fs.writeFileSync(STUDENTS_FILE, JSON.stringify(existingData, null, 2));
    },
    getAllStudents: function () {
        exports.storageUtils.initStorage();
        var fileContent = fs.readFileSync(STUDENTS_FILE, 'utf-8');
        return JSON.parse(fileContent);
    }
};
