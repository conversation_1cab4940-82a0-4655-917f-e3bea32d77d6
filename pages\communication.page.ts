import { Page, Locator } from '@playwright/test';
import { communicationLocators, communicationRoleLocators } from './communication.locators';

export class CommunicationPage {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    // Locator getters
    get communicationMenu(): Locator {
        return this.page.getByText(communicationRoleLocators.communicationMenu.text, {
            exact: communicationRoleLocators.communicationMenu.exact
        });
    }

    get messageGroupsLink(): Locator {
        return this.page.getByRole(communicationRoleLocators.messageGroupsLink.role as 'link', {
            name: communicationRoleLocators.messageGroupsLink.name
        });
    }

    get bulkCommunicationsLink(): Locator {
        return this.page.getByRole(communicationRoleLocators.bulkCommunicationsLink.role as 'link', {
            name: communicationRoleLocators.bulkCommunicationsLink.name
        });
    }

    get triggeredCommunicationsLink(): Locator {
        return this.page.getByRole(communicationRoleLocators.triggeredCommunicationsLink.role as 'link', {
            name: communicationRoleLocators.triggeredCommunicationsLink.name
        });
    }

    get scheduledCommunicationsLink(): Locator {
        return this.page.getByRole(communicationRoleLocators.scheduledCommunicationsLink.role as 'link', {
            name: communicationRoleLocators.scheduledCommunicationsLink.name
        });
    }

    get surveyCommunicationsLink(): Locator {
        return this.page.getByRole(communicationRoleLocators.surveyCommunicationsLink.role as 'link', {
            name: communicationRoleLocators.surveyCommunicationsLink.name
        });
    }

    get addNewUserGroupButton(): Locator {
        return this.page.getByRole(communicationRoleLocators.addNewUserGroupButton.role as 'button', {
            name: communicationRoleLocators.addNewUserGroupButton.name
        });
    }

    get setupCommunicationButton(): Locator {
        return this.page.getByRole(communicationRoleLocators.setupCommunicationButton.role as 'button', {
            name: communicationRoleLocators.setupCommunicationButton.name
        });
    }

    get groupNameField(): Locator {
        return this.page.getByLabel(communicationRoleLocators.groupNameField.label);
    }

    get messageGroupDataGrid(): Locator {
        return this.page.locator(communicationLocators.messageGroups.dataGrid);
    }

    get messageGroupSearchBar(): Locator {
        return this.page.locator(communicationLocators.messageGroups.searchBar);
    }

    get applyLink(): Locator {
        return this.page.getByRole(communicationRoleLocators.applyLink.role as 'link', {
            name: communicationRoleLocators.applyLink.name
        });
    }

    get backNavigationCell(): Locator {
        return this.page.getByRole(communicationRoleLocators.backNavigationCell.role as 'cell', {
            name: communicationRoleLocators.backNavigationCell.name
        });
    }

    get bulkCommunicationsHeading(): Locator {
        return this.page.getByRole(communicationRoleLocators.bulkCommunicationsHeading.role as 'heading', {
            name: communicationRoleLocators.bulkCommunicationsHeading.name
        });
    }

    get triggeredCommunicationsHeading(): Locator {
        return this.page.getByRole(communicationRoleLocators.triggeredCommunicationsHeading.role as 'heading', {
            name: communicationRoleLocators.triggeredCommunicationsHeading.name
        });
    }

    get scheduledCommunicationsHeading(): Locator {
        return this.page.getByRole(communicationRoleLocators.scheduledCommunicationsHeading.role as 'heading', {
            name: communicationRoleLocators.scheduledCommunicationsHeading.name
        });
    }

    get messageNameCell(): Locator {
        return this.page.getByRole(communicationRoleLocators.messageNameCell.role as 'cell', {
            name: communicationRoleLocators.messageNameCell.name,
            exact: communicationRoleLocators.messageNameCell.exact
        });
    }

    // Action methods
    async navigateToMessageGroups(): Promise<void> {
        await this.communicationMenu.click();
        await this.messageGroupsLink.click();
    }

    async createNewMessageGroup(): Promise<void> {
        try {
            await this.addNewUserGroupButton.click();
            if (await this.groupNameField.isVisible()) {
                console.log("Group name field is visible");
            } else {
                console.log("Group name field is not visible");
            }
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Error in createNewMessageGroup:', error.message);
            } else {
                console.error('Error in createNewMessageGroup:', error);
            }
            throw error;
        }
    }

    async backToMessageGroups(): Promise<void> {
        await this.page.waitForTimeout(2000);
        await this.backNavigationCell.click();
        await this.page.waitForTimeout(3000);
    }

    async selectRandomMessageGroupFromGrid(): Promise<string> {
        try {
            const dataGridRows = await this.messageGroupDataGrid.all();
            const numberOfRows = dataGridRows.length;
            if (numberOfRows === 0) {
                throw new Error("No message groups found");
            }

            const groups: string[] = [];

            for (let i = 1; i <= numberOfRows; i++) {
                const elementXPath = `//table[@id='enrol-table']/tbody/tr[${i}]/td[1]`;
                const [option] = await this.page.$$(elementXPath);
                const optionText = await option.textContent();
                if (optionText) {
                    groups.push(optionText.trim());
                }
            }
            if (groups.length === 0) {
                throw new Error("No valid message groups found");
            }

            const randomSearch = groups[Math.floor(Math.random() * groups.length)];
            await this.messageGroupSearchBar.fill(randomSearch);
            console.log(`Random search: ${randomSearch}`);
            await this.page.waitForTimeout(5000);
            return randomSearch;
        } catch (error: unknown) {
            if (error instanceof Error) {
                console.error('Error in selectRandomMessageGroupFromGrid:', error.message);
            } else {
                console.error('Error in selectRandomMessageGroupFromGrid:', error);
            }
            throw error;
        }
    }

    async searchRandomMessageGroup(): Promise<string> {
        const randomGroup = await this.selectRandomMessageGroupFromGrid();
        await this.applyLink.click();
        await this.page.waitForTimeout(1000);
        return randomGroup;
    }

    async isMessageGroupDataGridVisible(): Promise<boolean> {
        return await this.messageGroupDataGrid.first().isVisible();
    }

    async verifyMessageNameFieldVisible(): Promise<void> {
        if (await this.messageNameCell.isVisible()) {
            console.log("Message name field is visible. Communication setup is successful");
        }
    }

    async navigateToBulkCommunications(): Promise<void> {
        await this.communicationMenu.click();
        await this.bulkCommunicationsLink.click();
        await this.bulkCommunicationsHeading.getByRole('button').click();
        await this.verifyMessageNameFieldVisible();
    }

    async navigateToTriggeredCommunications(): Promise<void> {
        await this.communicationMenu.click();
        await this.triggeredCommunicationsLink.click();
        await this.page.waitForTimeout(6000);
        await this.triggeredCommunicationsHeading.getByRole('button').click();
        await this.verifyMessageNameFieldVisible();
    }

    async navigateToScheduledCommunications(): Promise<void> {
        await this.communicationMenu.click();
        await this.scheduledCommunicationsLink.click();
        await this.scheduledCommunicationsHeading.getByRole('button').click();
        await this.verifyMessageNameFieldVisible();
    }

    async navigateToSurveyCommunications(): Promise<void> {
        await this.communicationMenu.click();
        await this.surveyCommunicationsLink.click();
        await this.page.waitForTimeout(3000);
        await this.setupCommunicationButton.nth(1).click();
        await this.page.waitForTimeout(3000);
        await this.verifyMessageNameFieldVisible();
    }
}
