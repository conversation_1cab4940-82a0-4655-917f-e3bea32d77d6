---
type: "always_apply"
---

### ✅ Rules to follow:

* Follow the refactor-guideline.md in docs
* Use TypeScript best practices.
* Use Platwright best practices.
* No changes to business logic or test flow — only structural refactor.
* All UI selectors should be declared as constants or pure functions in `*.locators.ts`.
* Maintain backwards compatibility — the tests should still run without breaking.
* Delete or mark old files as deprecated only **after** migration is complete.