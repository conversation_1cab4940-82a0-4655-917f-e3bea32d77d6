import { Page } from '@playwright/test';

/**
 * Navigates to the Financial Overview section in the Finances module.
 * @param {Page} page - The Playwright Page object.
 * @returns {Promise<void>}
 */
export async function financialOverviewProd(page: Page): Promise<void> {
    await page.click('text=Finances');
    await page.click('text=Financial Overview');
    await page.waitForTimeout(3000); // 3 seconds
}

/**
 * Navigates to the Debtors Accounts section in the Finances module.
 * @param {Page} page - The Playwright Page object.
 * @returns {Promise<void>}
 */
export async function debtorsAccountProd(page: Page): Promise<void> {
    await page.click('text=Finances');
    await page.click('text=Debtors Accounts');
    await page.waitForTimeout(3000); // 3 seconds
}

/**
 * Navigates to the Bank Import section in the Finances module.
 * @param {Page} page - The Playwright Page object.
 * @returns {Promise<void>}
 */
export async function bankImportProd(page: Page): Promise<void> {
    await page.click('text=Finances');
    await page.click('text=Bank Import');
    await page.waitForTimeout(3000); // 3 seconds
}

/**
 * Navigates to the Debit Orders section in the Finances module.
 * @param {Page} page - The Playwright Page object.
 * @returns {Promise<void>}
 */
export async function debitOrdersProd(page: Page): Promise<void> {
    await page.click('text=Finances');
    await page.click('text=Debit Orders');
    await page.waitForTimeout(3000); // 3 seconds
}

/**
 * Navigates to the Age Analysis section in the Finances module.
 * @param {Page} page - The Playwright Page object.
 * @returns {Promise<void>}
 */
export async function ageAnalysisProd(page: Page): Promise<void> {
    await page.click('text=Finances');
    await page.click('text=Age Analysis');
    await page.waitForTimeout(3000); // 3 seconds
}
